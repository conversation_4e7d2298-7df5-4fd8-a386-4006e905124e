import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  ListFunctionsRequest,
  ListFunctionsResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface ListFunctionsCommandInput extends ListFunctionsRequest {}
export interface ListFunctionsCommandOutput
  extends ListFunctionsResponse,
    __MetadataBearer {}
declare const ListFunctionsCommand_base: {
  new (
    input: ListFunctionsCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    ListFunctionsCommandInput,
    ListFunctionsCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    ...[input]: [] | [ListFunctionsCommandInput]
  ): import("@smithy/smithy-client").CommandImpl<
    ListFunctionsCommandInput,
    ListFunctionsCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class ListFunctionsCommand extends ListFunctionsCommand_base {
  protected static __types: {
    api: {
      input: ListFunctionsRequest;
      output: ListFunctionsResponse;
    };
    sdk: {
      input: ListFunctionsCommandInput;
      output: ListFunctionsCommandOutput;
    };
  };
}
