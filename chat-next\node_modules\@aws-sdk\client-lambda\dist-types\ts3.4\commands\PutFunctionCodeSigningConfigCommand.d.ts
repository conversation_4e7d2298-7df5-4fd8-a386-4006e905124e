import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  LambdaClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../LambdaClient";
import {
  PutFunctionCodeSigningConfigRequest,
  PutFunctionCodeSigningConfigResponse,
} from "../models/models_0";
export { __MetadataBearer };
export { $Command };
export interface PutFunctionCodeSigningConfigCommandInput
  extends PutFunctionCodeSigningConfigRequest {}
export interface PutFunctionCodeSigningConfigCommandOutput
  extends PutFunctionCodeSigningConfigResponse,
    __MetadataBearer {}
declare const PutFunctionCodeSigningConfigCommand_base: {
  new (
    input: PutFunctionCodeSigningConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutFunctionCodeSigningConfigCommandInput,
    PutFunctionCodeSigningConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutFunctionCodeSigningConfigCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutFunctionCodeSigningConfigCommandInput,
    PutFunctionCodeSigningConfigCommandOutput,
    LambdaClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutFunctionCodeSigningConfigCommand extends PutFunctionCodeSigningConfigCommand_base {
  protected static __types: {
    api: {
      input: PutFunctionCodeSigningConfigRequest;
      output: PutFunctionCodeSigningConfigResponse;
    };
    sdk: {
      input: PutFunctionCodeSigningConfigCommandInput;
      output: PutFunctionCodeSigningConfigCommandOutput;
    };
  };
}
