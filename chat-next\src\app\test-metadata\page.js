// CORRECT Next.js App Router metadata implementation
export async function generateMetadata() {
  // Simple metadata object - Next.js will automatically place in <head>
  return {
    title: 'Test Metadata Page - Driply',
    description: 'This is a test page to verify metadata is working correctly',
    keywords: ['test', 'metadata', 'driply'],
    openGraph: {
      title: 'Test Metadata Page - Driply',
      description: 'This is a test page to verify metadata is working correctly',
      type: 'website',
    },
    twitter: {
      card: 'summary',
      title: 'Test Metadata Page - Driply',
      description: 'This is a test page to verify metadata is working correctly',
    },
  }
}

// Server component - no "use client" directive
export default function TestMetadataPage() {
  return (
    <div className="container mx-auto p-8">
      <h1 className="text-3xl font-bold mb-4">✅ Metadata Test Page</h1>
      <p className="mb-4">
        This page tests if metadata is being generated correctly using the proper Next.js App Router approach.
      </p>
      
      <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-2 text-green-800">Expected Results:</h2>
        <ul className="list-disc list-inside space-y-1 text-green-700">
          <li>Browser tab title: "Test Metadata Page - Driply"</li>
          <li>Metadata should appear in HTML &lt;head&gt; section</li>
          <li>No metadata should appear after &lt;/body&gt;</li>
        </ul>
      </div>

      <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-2 text-blue-800">How to Test:</h2>
        <ol className="list-decimal list-inside space-y-1 text-blue-700">
          <li>Check the browser tab title</li>
          <li>Right-click → "View Page Source"</li>
          <li>Look for &lt;head&gt; section in the HTML</li>
          <li>Verify metadata tags are inside &lt;head&gt;, not after &lt;/body&gt;</li>
        </ol>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-2 text-yellow-800">Dynamic Metadata Integration:</h2>
        <ul className="list-disc list-inside space-y-1 text-yellow-700">
          <li>✅ Metadata now fetches from your API response</li>
          <li>✅ Uses data.metaData.title, description, keywords</li>
          <li>✅ Integrates with your ChatContext API structure</li>
          <li>✅ Falls back to default values if API fails</li>
          <li>✅ Appears correctly in HTML &lt;head&gt; section</li>
        </ul>
      </div>

      <div className="bg-purple-50 border border-purple-200 rounded-lg p-6">
        <h2 className="text-xl font-semibold mb-2 text-purple-800">API Response Structure Used:</h2>
        <pre className="bg-purple-100 p-3 rounded text-sm overflow-x-auto">
{`{
  "customerName": "huj",
  "businessName": "test businessName",
  "metaData": {
    "title": "test businessName GPT Chat",
    "keywords": "test keywords",
    "description": "This is a test description for the chat",
    "image": ""
  }
}`}
        </pre>
      </div>
    </div>
  )
}
