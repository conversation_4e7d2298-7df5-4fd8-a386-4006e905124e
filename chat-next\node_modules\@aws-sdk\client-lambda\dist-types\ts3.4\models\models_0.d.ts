import { ExceptionOptionType as __ExceptionOptionType } from "@smithy/smithy-client";
import { StreamingBlobTypes } from "@smithy/types";
import { LambdaServiceException as __BaseException } from "./LambdaServiceException";
export interface AccountLimit {
  TotalCodeSize?: number | undefined;
  CodeSizeUnzipped?: number | undefined;
  CodeSizeZipped?: number | undefined;
  ConcurrentExecutions?: number | undefined;
  UnreservedConcurrentExecutions?: number | undefined;
}
export interface AccountUsage {
  TotalCodeSize?: number | undefined;
  FunctionCount?: number | undefined;
}
export interface AddLayerVersionPermissionRequest {
  LayerName: string | undefined;
  VersionNumber: number | undefined;
  StatementId: string | undefined;
  Action: string | undefined;
  Principal: string | undefined;
  OrganizationId?: string | undefined;
  RevisionId?: string | undefined;
}
export interface AddLayerVersionPermissionResponse {
  Statement?: string | undefined;
  RevisionId?: string | undefined;
}
export declare class InvalidParameterValueException extends __BaseException {
  readonly name: "InvalidParameterValueException";
  readonly $fault: "client";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<InvalidParameterValueException, __BaseException>
  );
}
export declare class PolicyLengthExceededException extends __BaseException {
  readonly name: "PolicyLengthExceededException";
  readonly $fault: "client";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<PolicyLengthExceededException, __BaseException>
  );
}
export declare class PreconditionFailedException extends __BaseException {
  readonly name: "PreconditionFailedException";
  readonly $fault: "client";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<PreconditionFailedException, __BaseException>
  );
}
export declare class ResourceConflictException extends __BaseException {
  readonly name: "ResourceConflictException";
  readonly $fault: "client";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<ResourceConflictException, __BaseException>
  );
}
export declare class ResourceNotFoundException extends __BaseException {
  readonly name: "ResourceNotFoundException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<ResourceNotFoundException, __BaseException>
  );
}
export declare class ServiceException extends __BaseException {
  readonly name: "ServiceException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(opts: __ExceptionOptionType<ServiceException, __BaseException>);
}
export declare const ThrottleReason: {
  readonly CallerRateLimitExceeded: "CallerRateLimitExceeded";
  readonly ConcurrentInvocationLimitExceeded: "ConcurrentInvocationLimitExceeded";
  readonly ConcurrentSnapshotCreateLimitExceeded: "ConcurrentSnapshotCreateLimitExceeded";
  readonly FunctionInvocationRateLimitExceeded: "FunctionInvocationRateLimitExceeded";
  readonly ReservedFunctionConcurrentInvocationLimitExceeded: "ReservedFunctionConcurrentInvocationLimitExceeded";
  readonly ReservedFunctionInvocationRateLimitExceeded: "ReservedFunctionInvocationRateLimitExceeded";
};
export type ThrottleReason =
  (typeof ThrottleReason)[keyof typeof ThrottleReason];
export declare class TooManyRequestsException extends __BaseException {
  readonly name: "TooManyRequestsException";
  readonly $fault: "client";
  retryAfterSeconds?: string | undefined;
  Type?: string | undefined;
  Reason?: ThrottleReason | undefined;
  constructor(
    opts: __ExceptionOptionType<TooManyRequestsException, __BaseException>
  );
}
export declare const FunctionUrlAuthType: {
  readonly AWS_IAM: "AWS_IAM";
  readonly NONE: "NONE";
};
export type FunctionUrlAuthType =
  (typeof FunctionUrlAuthType)[keyof typeof FunctionUrlAuthType];
export interface AddPermissionRequest {
  FunctionName: string | undefined;
  StatementId: string | undefined;
  Action: string | undefined;
  Principal: string | undefined;
  SourceArn?: string | undefined;
  SourceAccount?: string | undefined;
  EventSourceToken?: string | undefined;
  Qualifier?: string | undefined;
  RevisionId?: string | undefined;
  PrincipalOrgID?: string | undefined;
  FunctionUrlAuthType?: FunctionUrlAuthType | undefined;
}
export interface AddPermissionResponse {
  Statement?: string | undefined;
}
export interface AliasRoutingConfiguration {
  AdditionalVersionWeights?: Record<string, number> | undefined;
}
export interface AliasConfiguration {
  AliasArn?: string | undefined;
  Name?: string | undefined;
  FunctionVersion?: string | undefined;
  Description?: string | undefined;
  RoutingConfig?: AliasRoutingConfiguration | undefined;
  RevisionId?: string | undefined;
}
export interface AllowedPublishers {
  SigningProfileVersionArns: string[] | undefined;
}
export declare const KafkaSchemaRegistryAuthType: {
  readonly BASIC_AUTH: "BASIC_AUTH";
  readonly CLIENT_CERTIFICATE_TLS_AUTH: "CLIENT_CERTIFICATE_TLS_AUTH";
  readonly SERVER_ROOT_CA_CERTIFICATE: "SERVER_ROOT_CA_CERTIFICATE";
};
export type KafkaSchemaRegistryAuthType =
  (typeof KafkaSchemaRegistryAuthType)[keyof typeof KafkaSchemaRegistryAuthType];
export interface KafkaSchemaRegistryAccessConfig {
  Type?: KafkaSchemaRegistryAuthType | undefined;
  URI?: string | undefined;
}
export declare const SchemaRegistryEventRecordFormat: {
  readonly JSON: "JSON";
  readonly SOURCE: "SOURCE";
};
export type SchemaRegistryEventRecordFormat =
  (typeof SchemaRegistryEventRecordFormat)[keyof typeof SchemaRegistryEventRecordFormat];
export declare const KafkaSchemaValidationAttribute: {
  readonly KEY: "KEY";
  readonly VALUE: "VALUE";
};
export type KafkaSchemaValidationAttribute =
  (typeof KafkaSchemaValidationAttribute)[keyof typeof KafkaSchemaValidationAttribute];
export interface KafkaSchemaValidationConfig {
  Attribute?: KafkaSchemaValidationAttribute | undefined;
}
export interface KafkaSchemaRegistryConfig {
  SchemaRegistryURI?: string | undefined;
  EventRecordFormat?: SchemaRegistryEventRecordFormat | undefined;
  AccessConfigs?: KafkaSchemaRegistryAccessConfig[] | undefined;
  SchemaValidationConfigs?: KafkaSchemaValidationConfig[] | undefined;
}
export interface AmazonManagedKafkaEventSourceConfig {
  ConsumerGroupId?: string | undefined;
  SchemaRegistryConfig?: KafkaSchemaRegistryConfig | undefined;
}
export declare const ApplicationLogLevel: {
  readonly Debug: "DEBUG";
  readonly Error: "ERROR";
  readonly Fatal: "FATAL";
  readonly Info: "INFO";
  readonly Trace: "TRACE";
  readonly Warn: "WARN";
};
export type ApplicationLogLevel =
  (typeof ApplicationLogLevel)[keyof typeof ApplicationLogLevel];
export declare const Architecture: {
  readonly arm64: "arm64";
  readonly x86_64: "x86_64";
};
export type Architecture = (typeof Architecture)[keyof typeof Architecture];
export declare const CodeSigningPolicy: {
  readonly Enforce: "Enforce";
  readonly Warn: "Warn";
};
export type CodeSigningPolicy =
  (typeof CodeSigningPolicy)[keyof typeof CodeSigningPolicy];
export interface CodeSigningPolicies {
  UntrustedArtifactOnDeployment?: CodeSigningPolicy | undefined;
}
export interface CreateCodeSigningConfigRequest {
  Description?: string | undefined;
  AllowedPublishers: AllowedPublishers | undefined;
  CodeSigningPolicies?: CodeSigningPolicies | undefined;
  Tags?: Record<string, string> | undefined;
}
export interface CodeSigningConfig {
  CodeSigningConfigId: string | undefined;
  CodeSigningConfigArn: string | undefined;
  Description?: string | undefined;
  AllowedPublishers: AllowedPublishers | undefined;
  CodeSigningPolicies: CodeSigningPolicies | undefined;
  LastModified: string | undefined;
}
export interface CreateCodeSigningConfigResponse {
  CodeSigningConfig: CodeSigningConfig | undefined;
}
export interface DeleteCodeSigningConfigRequest {
  CodeSigningConfigArn: string | undefined;
}
export interface DeleteCodeSigningConfigResponse {}
export interface GetCodeSigningConfigRequest {
  CodeSigningConfigArn: string | undefined;
}
export interface GetCodeSigningConfigResponse {
  CodeSigningConfig: CodeSigningConfig | undefined;
}
export interface ListCodeSigningConfigsRequest {
  Marker?: string | undefined;
  MaxItems?: number | undefined;
}
export interface ListCodeSigningConfigsResponse {
  NextMarker?: string | undefined;
  CodeSigningConfigs?: CodeSigningConfig[] | undefined;
}
export interface ListFunctionsByCodeSigningConfigRequest {
  CodeSigningConfigArn: string | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
}
export interface ListFunctionsByCodeSigningConfigResponse {
  NextMarker?: string | undefined;
  FunctionArns?: string[] | undefined;
}
export interface UpdateCodeSigningConfigRequest {
  CodeSigningConfigArn: string | undefined;
  Description?: string | undefined;
  AllowedPublishers?: AllowedPublishers | undefined;
  CodeSigningPolicies?: CodeSigningPolicies | undefined;
}
export interface UpdateCodeSigningConfigResponse {
  CodeSigningConfig: CodeSigningConfig | undefined;
}
export interface OnFailure {
  Destination?: string | undefined;
}
export interface OnSuccess {
  Destination?: string | undefined;
}
export interface DestinationConfig {
  OnSuccess?: OnSuccess | undefined;
  OnFailure?: OnFailure | undefined;
}
export declare const FullDocument: {
  readonly Default: "Default";
  readonly UpdateLookup: "UpdateLookup";
};
export type FullDocument = (typeof FullDocument)[keyof typeof FullDocument];
export interface DocumentDBEventSourceConfig {
  DatabaseName?: string | undefined;
  CollectionName?: string | undefined;
  FullDocument?: FullDocument | undefined;
}
export interface Filter {
  Pattern?: string | undefined;
}
export interface FilterCriteria {
  Filters?: Filter[] | undefined;
}
export declare const FunctionResponseType: {
  readonly ReportBatchItemFailures: "ReportBatchItemFailures";
};
export type FunctionResponseType =
  (typeof FunctionResponseType)[keyof typeof FunctionResponseType];
export declare const EventSourceMappingMetric: {
  readonly EventCount: "EventCount";
};
export type EventSourceMappingMetric =
  (typeof EventSourceMappingMetric)[keyof typeof EventSourceMappingMetric];
export interface EventSourceMappingMetricsConfig {
  Metrics?: EventSourceMappingMetric[] | undefined;
}
export interface ProvisionedPollerConfig {
  MinimumPollers?: number | undefined;
  MaximumPollers?: number | undefined;
}
export interface ScalingConfig {
  MaximumConcurrency?: number | undefined;
}
export declare const EndPointType: {
  readonly KAFKA_BOOTSTRAP_SERVERS: "KAFKA_BOOTSTRAP_SERVERS";
};
export type EndPointType = (typeof EndPointType)[keyof typeof EndPointType];
export interface SelfManagedEventSource {
  Endpoints?: Partial<Record<EndPointType, string[]>> | undefined;
}
export interface SelfManagedKafkaEventSourceConfig {
  ConsumerGroupId?: string | undefined;
  SchemaRegistryConfig?: KafkaSchemaRegistryConfig | undefined;
}
export declare const SourceAccessType: {
  readonly BASIC_AUTH: "BASIC_AUTH";
  readonly CLIENT_CERTIFICATE_TLS_AUTH: "CLIENT_CERTIFICATE_TLS_AUTH";
  readonly SASL_SCRAM_256_AUTH: "SASL_SCRAM_256_AUTH";
  readonly SASL_SCRAM_512_AUTH: "SASL_SCRAM_512_AUTH";
  readonly SERVER_ROOT_CA_CERTIFICATE: "SERVER_ROOT_CA_CERTIFICATE";
  readonly VIRTUAL_HOST: "VIRTUAL_HOST";
  readonly VPC_SECURITY_GROUP: "VPC_SECURITY_GROUP";
  readonly VPC_SUBNET: "VPC_SUBNET";
};
export type SourceAccessType =
  (typeof SourceAccessType)[keyof typeof SourceAccessType];
export interface SourceAccessConfiguration {
  Type?: SourceAccessType | undefined;
  URI?: string | undefined;
}
export declare const EventSourcePosition: {
  readonly AT_TIMESTAMP: "AT_TIMESTAMP";
  readonly LATEST: "LATEST";
  readonly TRIM_HORIZON: "TRIM_HORIZON";
};
export type EventSourcePosition =
  (typeof EventSourcePosition)[keyof typeof EventSourcePosition];
export interface CreateEventSourceMappingRequest {
  EventSourceArn?: string | undefined;
  FunctionName: string | undefined;
  Enabled?: boolean | undefined;
  BatchSize?: number | undefined;
  FilterCriteria?: FilterCriteria | undefined;
  MaximumBatchingWindowInSeconds?: number | undefined;
  ParallelizationFactor?: number | undefined;
  StartingPosition?: EventSourcePosition | undefined;
  StartingPositionTimestamp?: Date | undefined;
  DestinationConfig?: DestinationConfig | undefined;
  MaximumRecordAgeInSeconds?: number | undefined;
  BisectBatchOnFunctionError?: boolean | undefined;
  MaximumRetryAttempts?: number | undefined;
  Tags?: Record<string, string> | undefined;
  TumblingWindowInSeconds?: number | undefined;
  Topics?: string[] | undefined;
  Queues?: string[] | undefined;
  SourceAccessConfigurations?: SourceAccessConfiguration[] | undefined;
  SelfManagedEventSource?: SelfManagedEventSource | undefined;
  FunctionResponseTypes?: FunctionResponseType[] | undefined;
  AmazonManagedKafkaEventSourceConfig?:
    | AmazonManagedKafkaEventSourceConfig
    | undefined;
  SelfManagedKafkaEventSourceConfig?:
    | SelfManagedKafkaEventSourceConfig
    | undefined;
  ScalingConfig?: ScalingConfig | undefined;
  DocumentDBEventSourceConfig?: DocumentDBEventSourceConfig | undefined;
  KMSKeyArn?: string | undefined;
  MetricsConfig?: EventSourceMappingMetricsConfig | undefined;
  ProvisionedPollerConfig?: ProvisionedPollerConfig | undefined;
}
export interface FilterCriteriaError {
  ErrorCode?: string | undefined;
  Message?: string | undefined;
}
export interface EventSourceMappingConfiguration {
  UUID?: string | undefined;
  StartingPosition?: EventSourcePosition | undefined;
  StartingPositionTimestamp?: Date | undefined;
  BatchSize?: number | undefined;
  MaximumBatchingWindowInSeconds?: number | undefined;
  ParallelizationFactor?: number | undefined;
  EventSourceArn?: string | undefined;
  FilterCriteria?: FilterCriteria | undefined;
  FunctionArn?: string | undefined;
  LastModified?: Date | undefined;
  LastProcessingResult?: string | undefined;
  State?: string | undefined;
  StateTransitionReason?: string | undefined;
  DestinationConfig?: DestinationConfig | undefined;
  Topics?: string[] | undefined;
  Queues?: string[] | undefined;
  SourceAccessConfigurations?: SourceAccessConfiguration[] | undefined;
  SelfManagedEventSource?: SelfManagedEventSource | undefined;
  MaximumRecordAgeInSeconds?: number | undefined;
  BisectBatchOnFunctionError?: boolean | undefined;
  MaximumRetryAttempts?: number | undefined;
  TumblingWindowInSeconds?: number | undefined;
  FunctionResponseTypes?: FunctionResponseType[] | undefined;
  AmazonManagedKafkaEventSourceConfig?:
    | AmazonManagedKafkaEventSourceConfig
    | undefined;
  SelfManagedKafkaEventSourceConfig?:
    | SelfManagedKafkaEventSourceConfig
    | undefined;
  ScalingConfig?: ScalingConfig | undefined;
  DocumentDBEventSourceConfig?: DocumentDBEventSourceConfig | undefined;
  KMSKeyArn?: string | undefined;
  FilterCriteriaError?: FilterCriteriaError | undefined;
  EventSourceMappingArn?: string | undefined;
  MetricsConfig?: EventSourceMappingMetricsConfig | undefined;
  ProvisionedPollerConfig?: ProvisionedPollerConfig | undefined;
}
export interface DeleteEventSourceMappingRequest {
  UUID: string | undefined;
}
export declare class ResourceInUseException extends __BaseException {
  readonly name: "ResourceInUseException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<ResourceInUseException, __BaseException>
  );
}
export interface GetEventSourceMappingRequest {
  UUID: string | undefined;
}
export interface ListEventSourceMappingsRequest {
  EventSourceArn?: string | undefined;
  FunctionName?: string | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
}
export interface ListEventSourceMappingsResponse {
  NextMarker?: string | undefined;
  EventSourceMappings?: EventSourceMappingConfiguration[] | undefined;
}
export interface UpdateEventSourceMappingRequest {
  UUID: string | undefined;
  FunctionName?: string | undefined;
  Enabled?: boolean | undefined;
  BatchSize?: number | undefined;
  FilterCriteria?: FilterCriteria | undefined;
  MaximumBatchingWindowInSeconds?: number | undefined;
  DestinationConfig?: DestinationConfig | undefined;
  MaximumRecordAgeInSeconds?: number | undefined;
  BisectBatchOnFunctionError?: boolean | undefined;
  MaximumRetryAttempts?: number | undefined;
  ParallelizationFactor?: number | undefined;
  SourceAccessConfigurations?: SourceAccessConfiguration[] | undefined;
  TumblingWindowInSeconds?: number | undefined;
  FunctionResponseTypes?: FunctionResponseType[] | undefined;
  ScalingConfig?: ScalingConfig | undefined;
  AmazonManagedKafkaEventSourceConfig?:
    | AmazonManagedKafkaEventSourceConfig
    | undefined;
  SelfManagedKafkaEventSourceConfig?:
    | SelfManagedKafkaEventSourceConfig
    | undefined;
  DocumentDBEventSourceConfig?: DocumentDBEventSourceConfig | undefined;
  KMSKeyArn?: string | undefined;
  MetricsConfig?: EventSourceMappingMetricsConfig | undefined;
  ProvisionedPollerConfig?: ProvisionedPollerConfig | undefined;
}
export declare class CodeSigningConfigNotFoundException extends __BaseException {
  readonly name: "CodeSigningConfigNotFoundException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<
      CodeSigningConfigNotFoundException,
      __BaseException
    >
  );
}
export declare class CodeStorageExceededException extends __BaseException {
  readonly name: "CodeStorageExceededException";
  readonly $fault: "client";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<CodeStorageExceededException, __BaseException>
  );
}
export declare class CodeVerificationFailedException extends __BaseException {
  readonly name: "CodeVerificationFailedException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<
      CodeVerificationFailedException,
      __BaseException
    >
  );
}
export interface FunctionCode {
  ZipFile?: Uint8Array | undefined;
  S3Bucket?: string | undefined;
  S3Key?: string | undefined;
  S3ObjectVersion?: string | undefined;
  ImageUri?: string | undefined;
  SourceKMSKeyArn?: string | undefined;
}
export interface DeadLetterConfig {
  TargetArn?: string | undefined;
}
export interface Environment {
  Variables?: Record<string, string> | undefined;
}
export interface EphemeralStorage {
  Size: number | undefined;
}
export interface FileSystemConfig {
  Arn: string | undefined;
  LocalMountPath: string | undefined;
}
export interface ImageConfig {
  EntryPoint?: string[] | undefined;
  Command?: string[] | undefined;
  WorkingDirectory?: string | undefined;
}
export declare const LogFormat: {
  readonly Json: "JSON";
  readonly Text: "Text";
};
export type LogFormat = (typeof LogFormat)[keyof typeof LogFormat];
export declare const SystemLogLevel: {
  readonly Debug: "DEBUG";
  readonly Info: "INFO";
  readonly Warn: "WARN";
};
export type SystemLogLevel =
  (typeof SystemLogLevel)[keyof typeof SystemLogLevel];
export interface LoggingConfig {
  LogFormat?: LogFormat | undefined;
  ApplicationLogLevel?: ApplicationLogLevel | undefined;
  SystemLogLevel?: SystemLogLevel | undefined;
  LogGroup?: string | undefined;
}
export declare const PackageType: {
  readonly Image: "Image";
  readonly Zip: "Zip";
};
export type PackageType = (typeof PackageType)[keyof typeof PackageType];
export declare const Runtime: {
  readonly dotnet6: "dotnet6";
  readonly dotnet8: "dotnet8";
  readonly dotnetcore10: "dotnetcore1.0";
  readonly dotnetcore20: "dotnetcore2.0";
  readonly dotnetcore21: "dotnetcore2.1";
  readonly dotnetcore31: "dotnetcore3.1";
  readonly go1x: "go1.x";
  readonly java11: "java11";
  readonly java17: "java17";
  readonly java21: "java21";
  readonly java8: "java8";
  readonly java8al2: "java8.al2";
  readonly nodejs: "nodejs";
  readonly nodejs10x: "nodejs10.x";
  readonly nodejs12x: "nodejs12.x";
  readonly nodejs14x: "nodejs14.x";
  readonly nodejs16x: "nodejs16.x";
  readonly nodejs18x: "nodejs18.x";
  readonly nodejs20x: "nodejs20.x";
  readonly nodejs22x: "nodejs22.x";
  readonly nodejs43: "nodejs4.3";
  readonly nodejs43edge: "nodejs4.3-edge";
  readonly nodejs610: "nodejs6.10";
  readonly nodejs810: "nodejs8.10";
  readonly provided: "provided";
  readonly providedal2: "provided.al2";
  readonly providedal2023: "provided.al2023";
  readonly python27: "python2.7";
  readonly python310: "python3.10";
  readonly python311: "python3.11";
  readonly python312: "python3.12";
  readonly python313: "python3.13";
  readonly python36: "python3.6";
  readonly python37: "python3.7";
  readonly python38: "python3.8";
  readonly python39: "python3.9";
  readonly ruby25: "ruby2.5";
  readonly ruby27: "ruby2.7";
  readonly ruby32: "ruby3.2";
  readonly ruby33: "ruby3.3";
  readonly ruby34: "ruby3.4";
};
export type Runtime = (typeof Runtime)[keyof typeof Runtime];
export declare const SnapStartApplyOn: {
  readonly None: "None";
  readonly PublishedVersions: "PublishedVersions";
};
export type SnapStartApplyOn =
  (typeof SnapStartApplyOn)[keyof typeof SnapStartApplyOn];
export interface SnapStart {
  ApplyOn?: SnapStartApplyOn | undefined;
}
export declare const TracingMode: {
  readonly Active: "Active";
  readonly PassThrough: "PassThrough";
};
export type TracingMode = (typeof TracingMode)[keyof typeof TracingMode];
export interface TracingConfig {
  Mode?: TracingMode | undefined;
}
export interface VpcConfig {
  SubnetIds?: string[] | undefined;
  SecurityGroupIds?: string[] | undefined;
  Ipv6AllowedForDualStack?: boolean | undefined;
}
export interface CreateFunctionRequest {
  FunctionName: string | undefined;
  Runtime?: Runtime | undefined;
  Role: string | undefined;
  Handler?: string | undefined;
  Code: FunctionCode | undefined;
  Description?: string | undefined;
  Timeout?: number | undefined;
  MemorySize?: number | undefined;
  Publish?: boolean | undefined;
  VpcConfig?: VpcConfig | undefined;
  PackageType?: PackageType | undefined;
  DeadLetterConfig?: DeadLetterConfig | undefined;
  Environment?: Environment | undefined;
  KMSKeyArn?: string | undefined;
  TracingConfig?: TracingConfig | undefined;
  Tags?: Record<string, string> | undefined;
  Layers?: string[] | undefined;
  FileSystemConfigs?: FileSystemConfig[] | undefined;
  ImageConfig?: ImageConfig | undefined;
  CodeSigningConfigArn?: string | undefined;
  Architectures?: Architecture[] | undefined;
  EphemeralStorage?: EphemeralStorage | undefined;
  SnapStart?: SnapStart | undefined;
  LoggingConfig?: LoggingConfig | undefined;
}
export interface EnvironmentError {
  ErrorCode?: string | undefined;
  Message?: string | undefined;
}
export interface EnvironmentResponse {
  Variables?: Record<string, string> | undefined;
  Error?: EnvironmentError | undefined;
}
export interface ImageConfigError {
  ErrorCode?: string | undefined;
  Message?: string | undefined;
}
export interface ImageConfigResponse {
  ImageConfig?: ImageConfig | undefined;
  Error?: ImageConfigError | undefined;
}
export declare const LastUpdateStatus: {
  readonly Failed: "Failed";
  readonly InProgress: "InProgress";
  readonly Successful: "Successful";
};
export type LastUpdateStatus =
  (typeof LastUpdateStatus)[keyof typeof LastUpdateStatus];
export declare const LastUpdateStatusReasonCode: {
  readonly DisabledKMSKey: "DisabledKMSKey";
  readonly EFSIOError: "EFSIOError";
  readonly EFSMountConnectivityError: "EFSMountConnectivityError";
  readonly EFSMountFailure: "EFSMountFailure";
  readonly EFSMountTimeout: "EFSMountTimeout";
  readonly EniLimitExceeded: "EniLimitExceeded";
  readonly FunctionError: "FunctionError";
  readonly ImageAccessDenied: "ImageAccessDenied";
  readonly ImageDeleted: "ImageDeleted";
  readonly InsufficientRolePermissions: "InsufficientRolePermissions";
  readonly InternalError: "InternalError";
  readonly InvalidConfiguration: "InvalidConfiguration";
  readonly InvalidImage: "InvalidImage";
  readonly InvalidRuntime: "InvalidRuntime";
  readonly InvalidSecurityGroup: "InvalidSecurityGroup";
  readonly InvalidStateKMSKey: "InvalidStateKMSKey";
  readonly InvalidSubnet: "InvalidSubnet";
  readonly InvalidZipFileException: "InvalidZipFileException";
  readonly KMSKeyAccessDenied: "KMSKeyAccessDenied";
  readonly KMSKeyNotFound: "KMSKeyNotFound";
  readonly SubnetOutOfIPAddresses: "SubnetOutOfIPAddresses";
};
export type LastUpdateStatusReasonCode =
  (typeof LastUpdateStatusReasonCode)[keyof typeof LastUpdateStatusReasonCode];
export interface Layer {
  Arn?: string | undefined;
  CodeSize?: number | undefined;
  SigningProfileVersionArn?: string | undefined;
  SigningJobArn?: string | undefined;
}
export interface RuntimeVersionError {
  ErrorCode?: string | undefined;
  Message?: string | undefined;
}
export interface RuntimeVersionConfig {
  RuntimeVersionArn?: string | undefined;
  Error?: RuntimeVersionError | undefined;
}
export declare const SnapStartOptimizationStatus: {
  readonly Off: "Off";
  readonly On: "On";
};
export type SnapStartOptimizationStatus =
  (typeof SnapStartOptimizationStatus)[keyof typeof SnapStartOptimizationStatus];
export interface SnapStartResponse {
  ApplyOn?: SnapStartApplyOn | undefined;
  OptimizationStatus?: SnapStartOptimizationStatus | undefined;
}
export declare const State: {
  readonly Active: "Active";
  readonly Failed: "Failed";
  readonly Inactive: "Inactive";
  readonly Pending: "Pending";
};
export type State = (typeof State)[keyof typeof State];
export declare const StateReasonCode: {
  readonly Creating: "Creating";
  readonly DisabledKMSKey: "DisabledKMSKey";
  readonly EFSIOError: "EFSIOError";
  readonly EFSMountConnectivityError: "EFSMountConnectivityError";
  readonly EFSMountFailure: "EFSMountFailure";
  readonly EFSMountTimeout: "EFSMountTimeout";
  readonly EniLimitExceeded: "EniLimitExceeded";
  readonly FunctionError: "FunctionError";
  readonly Idle: "Idle";
  readonly ImageAccessDenied: "ImageAccessDenied";
  readonly ImageDeleted: "ImageDeleted";
  readonly InsufficientRolePermissions: "InsufficientRolePermissions";
  readonly InternalError: "InternalError";
  readonly InvalidConfiguration: "InvalidConfiguration";
  readonly InvalidImage: "InvalidImage";
  readonly InvalidRuntime: "InvalidRuntime";
  readonly InvalidSecurityGroup: "InvalidSecurityGroup";
  readonly InvalidStateKMSKey: "InvalidStateKMSKey";
  readonly InvalidSubnet: "InvalidSubnet";
  readonly InvalidZipFileException: "InvalidZipFileException";
  readonly KMSKeyAccessDenied: "KMSKeyAccessDenied";
  readonly KMSKeyNotFound: "KMSKeyNotFound";
  readonly Restoring: "Restoring";
  readonly SubnetOutOfIPAddresses: "SubnetOutOfIPAddresses";
};
export type StateReasonCode =
  (typeof StateReasonCode)[keyof typeof StateReasonCode];
export interface TracingConfigResponse {
  Mode?: TracingMode | undefined;
}
export interface VpcConfigResponse {
  SubnetIds?: string[] | undefined;
  SecurityGroupIds?: string[] | undefined;
  VpcId?: string | undefined;
  Ipv6AllowedForDualStack?: boolean | undefined;
}
export interface FunctionConfiguration {
  FunctionName?: string | undefined;
  FunctionArn?: string | undefined;
  Runtime?: Runtime | undefined;
  Role?: string | undefined;
  Handler?: string | undefined;
  CodeSize?: number | undefined;
  Description?: string | undefined;
  Timeout?: number | undefined;
  MemorySize?: number | undefined;
  LastModified?: string | undefined;
  CodeSha256?: string | undefined;
  Version?: string | undefined;
  VpcConfig?: VpcConfigResponse | undefined;
  DeadLetterConfig?: DeadLetterConfig | undefined;
  Environment?: EnvironmentResponse | undefined;
  KMSKeyArn?: string | undefined;
  TracingConfig?: TracingConfigResponse | undefined;
  MasterArn?: string | undefined;
  RevisionId?: string | undefined;
  Layers?: Layer[] | undefined;
  State?: State | undefined;
  StateReason?: string | undefined;
  StateReasonCode?: StateReasonCode | undefined;
  LastUpdateStatus?: LastUpdateStatus | undefined;
  LastUpdateStatusReason?: string | undefined;
  LastUpdateStatusReasonCode?: LastUpdateStatusReasonCode | undefined;
  FileSystemConfigs?: FileSystemConfig[] | undefined;
  PackageType?: PackageType | undefined;
  ImageConfigResponse?: ImageConfigResponse | undefined;
  SigningProfileVersionArn?: string | undefined;
  SigningJobArn?: string | undefined;
  Architectures?: Architecture[] | undefined;
  EphemeralStorage?: EphemeralStorage | undefined;
  SnapStart?: SnapStartResponse | undefined;
  RuntimeVersionConfig?: RuntimeVersionConfig | undefined;
  LoggingConfig?: LoggingConfig | undefined;
}
export declare class InvalidCodeSignatureException extends __BaseException {
  readonly name: "InvalidCodeSignatureException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<InvalidCodeSignatureException, __BaseException>
  );
}
export interface Cors {
  AllowCredentials?: boolean | undefined;
  AllowHeaders?: string[] | undefined;
  AllowMethods?: string[] | undefined;
  AllowOrigins?: string[] | undefined;
  ExposeHeaders?: string[] | undefined;
  MaxAge?: number | undefined;
}
export declare const InvokeMode: {
  readonly BUFFERED: "BUFFERED";
  readonly RESPONSE_STREAM: "RESPONSE_STREAM";
};
export type InvokeMode = (typeof InvokeMode)[keyof typeof InvokeMode];
export interface CreateFunctionUrlConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
  AuthType: FunctionUrlAuthType | undefined;
  Cors?: Cors | undefined;
  InvokeMode?: InvokeMode | undefined;
}
export interface CreateFunctionUrlConfigResponse {
  FunctionUrl: string | undefined;
  FunctionArn: string | undefined;
  AuthType: FunctionUrlAuthType | undefined;
  Cors?: Cors | undefined;
  CreationTime: string | undefined;
  InvokeMode?: InvokeMode | undefined;
}
export interface DeleteFunctionRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
}
export interface DeleteFunctionCodeSigningConfigRequest {
  FunctionName: string | undefined;
}
export interface DeleteFunctionConcurrencyRequest {
  FunctionName: string | undefined;
}
export interface DeleteFunctionEventInvokeConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
}
export interface DeleteFunctionUrlConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
}
export interface GetFunctionRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
}
export interface FunctionCodeLocation {
  RepositoryType?: string | undefined;
  Location?: string | undefined;
  ImageUri?: string | undefined;
  ResolvedImageUri?: string | undefined;
  SourceKMSKeyArn?: string | undefined;
}
export interface Concurrency {
  ReservedConcurrentExecutions?: number | undefined;
}
export interface TagsError {
  ErrorCode: string | undefined;
  Message: string | undefined;
}
export interface GetFunctionResponse {
  Configuration?: FunctionConfiguration | undefined;
  Code?: FunctionCodeLocation | undefined;
  Tags?: Record<string, string> | undefined;
  TagsError?: TagsError | undefined;
  Concurrency?: Concurrency | undefined;
}
export interface GetFunctionCodeSigningConfigRequest {
  FunctionName: string | undefined;
}
export interface GetFunctionCodeSigningConfigResponse {
  CodeSigningConfigArn: string | undefined;
  FunctionName: string | undefined;
}
export interface GetFunctionConcurrencyRequest {
  FunctionName: string | undefined;
}
export interface GetFunctionConcurrencyResponse {
  ReservedConcurrentExecutions?: number | undefined;
}
export interface GetFunctionConfigurationRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
}
export interface FunctionEventInvokeConfig {
  LastModified?: Date | undefined;
  FunctionArn?: string | undefined;
  MaximumRetryAttempts?: number | undefined;
  MaximumEventAgeInSeconds?: number | undefined;
  DestinationConfig?: DestinationConfig | undefined;
}
export interface GetFunctionEventInvokeConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
}
export interface GetFunctionRecursionConfigRequest {
  FunctionName: string | undefined;
}
export declare const RecursiveLoop: {
  readonly Allow: "Allow";
  readonly Terminate: "Terminate";
};
export type RecursiveLoop = (typeof RecursiveLoop)[keyof typeof RecursiveLoop];
export interface GetFunctionRecursionConfigResponse {
  RecursiveLoop?: RecursiveLoop | undefined;
}
export interface GetFunctionUrlConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
}
export interface GetFunctionUrlConfigResponse {
  FunctionUrl: string | undefined;
  FunctionArn: string | undefined;
  AuthType: FunctionUrlAuthType | undefined;
  Cors?: Cors | undefined;
  CreationTime: string | undefined;
  LastModifiedTime: string | undefined;
  InvokeMode?: InvokeMode | undefined;
}
export interface GetPolicyRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
}
export interface GetPolicyResponse {
  Policy?: string | undefined;
  RevisionId?: string | undefined;
}
export interface GetRuntimeManagementConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
}
export declare const UpdateRuntimeOn: {
  readonly Auto: "Auto";
  readonly FunctionUpdate: "FunctionUpdate";
  readonly Manual: "Manual";
};
export type UpdateRuntimeOn =
  (typeof UpdateRuntimeOn)[keyof typeof UpdateRuntimeOn];
export interface GetRuntimeManagementConfigResponse {
  UpdateRuntimeOn?: UpdateRuntimeOn | undefined;
  RuntimeVersionArn?: string | undefined;
  FunctionArn?: string | undefined;
}
export declare class EC2AccessDeniedException extends __BaseException {
  readonly name: "EC2AccessDeniedException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<EC2AccessDeniedException, __BaseException>
  );
}
export declare class EC2ThrottledException extends __BaseException {
  readonly name: "EC2ThrottledException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<EC2ThrottledException, __BaseException>
  );
}
export declare class EC2UnexpectedException extends __BaseException {
  readonly name: "EC2UnexpectedException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  EC2ErrorCode?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<EC2UnexpectedException, __BaseException>
  );
}
export declare class EFSIOException extends __BaseException {
  readonly name: "EFSIOException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(opts: __ExceptionOptionType<EFSIOException, __BaseException>);
}
export declare class EFSMountConnectivityException extends __BaseException {
  readonly name: "EFSMountConnectivityException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<EFSMountConnectivityException, __BaseException>
  );
}
export declare class EFSMountFailureException extends __BaseException {
  readonly name: "EFSMountFailureException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<EFSMountFailureException, __BaseException>
  );
}
export declare class EFSMountTimeoutException extends __BaseException {
  readonly name: "EFSMountTimeoutException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<EFSMountTimeoutException, __BaseException>
  );
}
export declare class ENILimitReachedException extends __BaseException {
  readonly name: "ENILimitReachedException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<ENILimitReachedException, __BaseException>
  );
}
export declare class InvalidRequestContentException extends __BaseException {
  readonly name: "InvalidRequestContentException";
  readonly $fault: "client";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<InvalidRequestContentException, __BaseException>
  );
}
export declare class InvalidRuntimeException extends __BaseException {
  readonly name: "InvalidRuntimeException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<InvalidRuntimeException, __BaseException>
  );
}
export declare class InvalidSecurityGroupIDException extends __BaseException {
  readonly name: "InvalidSecurityGroupIDException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<
      InvalidSecurityGroupIDException,
      __BaseException
    >
  );
}
export declare class InvalidSubnetIDException extends __BaseException {
  readonly name: "InvalidSubnetIDException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<InvalidSubnetIDException, __BaseException>
  );
}
export declare class InvalidZipFileException extends __BaseException {
  readonly name: "InvalidZipFileException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<InvalidZipFileException, __BaseException>
  );
}
export declare const InvocationType: {
  readonly DryRun: "DryRun";
  readonly Event: "Event";
  readonly RequestResponse: "RequestResponse";
};
export type InvocationType =
  (typeof InvocationType)[keyof typeof InvocationType];
export declare const LogType: {
  readonly None: "None";
  readonly Tail: "Tail";
};
export type LogType = (typeof LogType)[keyof typeof LogType];
export interface InvocationRequest {
  FunctionName: string | undefined;
  InvocationType?: InvocationType | undefined;
  LogType?: LogType | undefined;
  ClientContext?: string | undefined;
  Payload?: Uint8Array | undefined;
  Qualifier?: string | undefined;
}
export interface InvocationResponse {
  StatusCode?: number | undefined;
  FunctionError?: string | undefined;
  LogResult?: string | undefined;
  Payload?: Uint8Array | undefined;
  ExecutedVersion?: string | undefined;
}
export declare class KMSAccessDeniedException extends __BaseException {
  readonly name: "KMSAccessDeniedException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<KMSAccessDeniedException, __BaseException>
  );
}
export declare class KMSDisabledException extends __BaseException {
  readonly name: "KMSDisabledException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<KMSDisabledException, __BaseException>
  );
}
export declare class KMSInvalidStateException extends __BaseException {
  readonly name: "KMSInvalidStateException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<KMSInvalidStateException, __BaseException>
  );
}
export declare class KMSNotFoundException extends __BaseException {
  readonly name: "KMSNotFoundException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<KMSNotFoundException, __BaseException>
  );
}
export declare class RecursiveInvocationException extends __BaseException {
  readonly name: "RecursiveInvocationException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<RecursiveInvocationException, __BaseException>
  );
}
export declare class RequestTooLargeException extends __BaseException {
  readonly name: "RequestTooLargeException";
  readonly $fault: "client";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<RequestTooLargeException, __BaseException>
  );
}
export declare class ResourceNotReadyException extends __BaseException {
  readonly name: "ResourceNotReadyException";
  readonly $fault: "server";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<ResourceNotReadyException, __BaseException>
  );
}
export declare class SnapStartException extends __BaseException {
  readonly name: "SnapStartException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(opts: __ExceptionOptionType<SnapStartException, __BaseException>);
}
export declare class SnapStartNotReadyException extends __BaseException {
  readonly name: "SnapStartNotReadyException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<SnapStartNotReadyException, __BaseException>
  );
}
export declare class SnapStartTimeoutException extends __BaseException {
  readonly name: "SnapStartTimeoutException";
  readonly $fault: "client";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<SnapStartTimeoutException, __BaseException>
  );
}
export declare class SubnetIPAddressLimitReachedException extends __BaseException {
  readonly name: "SubnetIPAddressLimitReachedException";
  readonly $fault: "server";
  Type?: string | undefined;
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<
      SubnetIPAddressLimitReachedException,
      __BaseException
    >
  );
}
export declare class UnsupportedMediaTypeException extends __BaseException {
  readonly name: "UnsupportedMediaTypeException";
  readonly $fault: "client";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<UnsupportedMediaTypeException, __BaseException>
  );
}
export interface InvokeAsyncRequest {
  FunctionName: string | undefined;
  InvokeArgs: StreamingBlobTypes | undefined;
}
export interface InvokeAsyncResponse {
  Status?: number | undefined;
}
export declare const ResponseStreamingInvocationType: {
  readonly DryRun: "DryRun";
  readonly RequestResponse: "RequestResponse";
};
export type ResponseStreamingInvocationType =
  (typeof ResponseStreamingInvocationType)[keyof typeof ResponseStreamingInvocationType];
export interface InvokeWithResponseStreamRequest {
  FunctionName: string | undefined;
  InvocationType?: ResponseStreamingInvocationType | undefined;
  LogType?: LogType | undefined;
  ClientContext?: string | undefined;
  Qualifier?: string | undefined;
  Payload?: Uint8Array | undefined;
}
export interface InvokeWithResponseStreamCompleteEvent {
  ErrorCode?: string | undefined;
  ErrorDetails?: string | undefined;
  LogResult?: string | undefined;
}
export interface InvokeResponseStreamUpdate {
  Payload?: Uint8Array | undefined;
}
export type InvokeWithResponseStreamResponseEvent =
  | InvokeWithResponseStreamResponseEvent.InvokeCompleteMember
  | InvokeWithResponseStreamResponseEvent.PayloadChunkMember
  | InvokeWithResponseStreamResponseEvent.$UnknownMember;
export declare namespace InvokeWithResponseStreamResponseEvent {
  interface PayloadChunkMember {
    PayloadChunk: InvokeResponseStreamUpdate;
    InvokeComplete?: never;
    $unknown?: never;
  }
  interface InvokeCompleteMember {
    PayloadChunk?: never;
    InvokeComplete: InvokeWithResponseStreamCompleteEvent;
    $unknown?: never;
  }
  interface $UnknownMember {
    PayloadChunk?: never;
    InvokeComplete?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    PayloadChunk: (value: InvokeResponseStreamUpdate) => T;
    InvokeComplete: (value: InvokeWithResponseStreamCompleteEvent) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: InvokeWithResponseStreamResponseEvent,
    visitor: Visitor<T>
  ) => T;
}
export interface InvokeWithResponseStreamResponse {
  StatusCode?: number | undefined;
  ExecutedVersion?: string | undefined;
  EventStream?:
    | AsyncIterable<InvokeWithResponseStreamResponseEvent>
    | undefined;
  ResponseStreamContentType?: string | undefined;
}
export interface ListFunctionEventInvokeConfigsRequest {
  FunctionName: string | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
}
export interface ListFunctionEventInvokeConfigsResponse {
  FunctionEventInvokeConfigs?: FunctionEventInvokeConfig[] | undefined;
  NextMarker?: string | undefined;
}
export declare const FunctionVersion: {
  readonly ALL: "ALL";
};
export type FunctionVersion =
  (typeof FunctionVersion)[keyof typeof FunctionVersion];
export interface ListFunctionsRequest {
  MasterRegion?: string | undefined;
  FunctionVersion?: FunctionVersion | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
}
export interface ListFunctionsResponse {
  NextMarker?: string | undefined;
  Functions?: FunctionConfiguration[] | undefined;
}
export interface ListFunctionUrlConfigsRequest {
  FunctionName: string | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
}
export interface FunctionUrlConfig {
  FunctionUrl: string | undefined;
  FunctionArn: string | undefined;
  CreationTime: string | undefined;
  LastModifiedTime: string | undefined;
  Cors?: Cors | undefined;
  AuthType: FunctionUrlAuthType | undefined;
  InvokeMode?: InvokeMode | undefined;
}
export interface ListFunctionUrlConfigsResponse {
  FunctionUrlConfigs: FunctionUrlConfig[] | undefined;
  NextMarker?: string | undefined;
}
export interface ListProvisionedConcurrencyConfigsRequest {
  FunctionName: string | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
}
export declare const ProvisionedConcurrencyStatusEnum: {
  readonly FAILED: "FAILED";
  readonly IN_PROGRESS: "IN_PROGRESS";
  readonly READY: "READY";
};
export type ProvisionedConcurrencyStatusEnum =
  (typeof ProvisionedConcurrencyStatusEnum)[keyof typeof ProvisionedConcurrencyStatusEnum];
export interface ProvisionedConcurrencyConfigListItem {
  FunctionArn?: string | undefined;
  RequestedProvisionedConcurrentExecutions?: number | undefined;
  AvailableProvisionedConcurrentExecutions?: number | undefined;
  AllocatedProvisionedConcurrentExecutions?: number | undefined;
  Status?: ProvisionedConcurrencyStatusEnum | undefined;
  StatusReason?: string | undefined;
  LastModified?: string | undefined;
}
export interface ListProvisionedConcurrencyConfigsResponse {
  ProvisionedConcurrencyConfigs?:
    | ProvisionedConcurrencyConfigListItem[]
    | undefined;
  NextMarker?: string | undefined;
}
export interface PutFunctionCodeSigningConfigRequest {
  CodeSigningConfigArn: string | undefined;
  FunctionName: string | undefined;
}
export interface PutFunctionCodeSigningConfigResponse {
  CodeSigningConfigArn: string | undefined;
  FunctionName: string | undefined;
}
export interface PutFunctionConcurrencyRequest {
  FunctionName: string | undefined;
  ReservedConcurrentExecutions: number | undefined;
}
export interface PutFunctionEventInvokeConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
  MaximumRetryAttempts?: number | undefined;
  MaximumEventAgeInSeconds?: number | undefined;
  DestinationConfig?: DestinationConfig | undefined;
}
export interface PutFunctionRecursionConfigRequest {
  FunctionName: string | undefined;
  RecursiveLoop: RecursiveLoop | undefined;
}
export interface PutFunctionRecursionConfigResponse {
  RecursiveLoop?: RecursiveLoop | undefined;
}
export interface PutRuntimeManagementConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
  UpdateRuntimeOn: UpdateRuntimeOn | undefined;
  RuntimeVersionArn?: string | undefined;
}
export interface PutRuntimeManagementConfigResponse {
  UpdateRuntimeOn: UpdateRuntimeOn | undefined;
  FunctionArn: string | undefined;
  RuntimeVersionArn?: string | undefined;
}
export interface UpdateFunctionCodeRequest {
  FunctionName: string | undefined;
  ZipFile?: Uint8Array | undefined;
  S3Bucket?: string | undefined;
  S3Key?: string | undefined;
  S3ObjectVersion?: string | undefined;
  ImageUri?: string | undefined;
  Publish?: boolean | undefined;
  DryRun?: boolean | undefined;
  RevisionId?: string | undefined;
  Architectures?: Architecture[] | undefined;
  SourceKMSKeyArn?: string | undefined;
}
export interface UpdateFunctionConfigurationRequest {
  FunctionName: string | undefined;
  Role?: string | undefined;
  Handler?: string | undefined;
  Description?: string | undefined;
  Timeout?: number | undefined;
  MemorySize?: number | undefined;
  VpcConfig?: VpcConfig | undefined;
  Environment?: Environment | undefined;
  Runtime?: Runtime | undefined;
  DeadLetterConfig?: DeadLetterConfig | undefined;
  KMSKeyArn?: string | undefined;
  TracingConfig?: TracingConfig | undefined;
  RevisionId?: string | undefined;
  Layers?: string[] | undefined;
  FileSystemConfigs?: FileSystemConfig[] | undefined;
  ImageConfig?: ImageConfig | undefined;
  EphemeralStorage?: EphemeralStorage | undefined;
  SnapStart?: SnapStart | undefined;
  LoggingConfig?: LoggingConfig | undefined;
}
export interface UpdateFunctionEventInvokeConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
  MaximumRetryAttempts?: number | undefined;
  MaximumEventAgeInSeconds?: number | undefined;
  DestinationConfig?: DestinationConfig | undefined;
}
export interface UpdateFunctionUrlConfigRequest {
  FunctionName: string | undefined;
  Qualifier?: string | undefined;
  AuthType?: FunctionUrlAuthType | undefined;
  Cors?: Cors | undefined;
  InvokeMode?: InvokeMode | undefined;
}
export interface UpdateFunctionUrlConfigResponse {
  FunctionUrl: string | undefined;
  FunctionArn: string | undefined;
  AuthType: FunctionUrlAuthType | undefined;
  Cors?: Cors | undefined;
  CreationTime: string | undefined;
  LastModifiedTime: string | undefined;
  InvokeMode?: InvokeMode | undefined;
}
export interface CreateAliasRequest {
  FunctionName: string | undefined;
  Name: string | undefined;
  FunctionVersion: string | undefined;
  Description?: string | undefined;
  RoutingConfig?: AliasRoutingConfiguration | undefined;
}
export interface DeleteAliasRequest {
  FunctionName: string | undefined;
  Name: string | undefined;
}
export interface GetAliasRequest {
  FunctionName: string | undefined;
  Name: string | undefined;
}
export interface ListAliasesRequest {
  FunctionName: string | undefined;
  FunctionVersion?: string | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
}
export interface ListAliasesResponse {
  NextMarker?: string | undefined;
  Aliases?: AliasConfiguration[] | undefined;
}
export interface UpdateAliasRequest {
  FunctionName: string | undefined;
  Name: string | undefined;
  FunctionVersion?: string | undefined;
  Description?: string | undefined;
  RoutingConfig?: AliasRoutingConfiguration | undefined;
  RevisionId?: string | undefined;
}
export interface ListVersionsByFunctionRequest {
  FunctionName: string | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
}
export interface ListVersionsByFunctionResponse {
  NextMarker?: string | undefined;
  Versions?: FunctionConfiguration[] | undefined;
}
export interface PublishVersionRequest {
  FunctionName: string | undefined;
  CodeSha256?: string | undefined;
  Description?: string | undefined;
  RevisionId?: string | undefined;
}
export interface GetAccountSettingsRequest {}
export interface GetAccountSettingsResponse {
  AccountLimit?: AccountLimit | undefined;
  AccountUsage?: AccountUsage | undefined;
}
export interface ListLayersRequest {
  CompatibleRuntime?: Runtime | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
  CompatibleArchitecture?: Architecture | undefined;
}
export interface LayerVersionsListItem {
  LayerVersionArn?: string | undefined;
  Version?: number | undefined;
  Description?: string | undefined;
  CreatedDate?: string | undefined;
  CompatibleRuntimes?: Runtime[] | undefined;
  LicenseInfo?: string | undefined;
  CompatibleArchitectures?: Architecture[] | undefined;
}
export interface LayersListItem {
  LayerName?: string | undefined;
  LayerArn?: string | undefined;
  LatestMatchingVersion?: LayerVersionsListItem | undefined;
}
export interface ListLayersResponse {
  NextMarker?: string | undefined;
  Layers?: LayersListItem[] | undefined;
}
export interface DeleteLayerVersionRequest {
  LayerName: string | undefined;
  VersionNumber: number | undefined;
}
export interface GetLayerVersionRequest {
  LayerName: string | undefined;
  VersionNumber: number | undefined;
}
export interface LayerVersionContentOutput {
  Location?: string | undefined;
  CodeSha256?: string | undefined;
  CodeSize?: number | undefined;
  SigningProfileVersionArn?: string | undefined;
  SigningJobArn?: string | undefined;
}
export interface GetLayerVersionResponse {
  Content?: LayerVersionContentOutput | undefined;
  LayerArn?: string | undefined;
  LayerVersionArn?: string | undefined;
  Description?: string | undefined;
  CreatedDate?: string | undefined;
  Version?: number | undefined;
  CompatibleRuntimes?: Runtime[] | undefined;
  LicenseInfo?: string | undefined;
  CompatibleArchitectures?: Architecture[] | undefined;
}
export interface GetLayerVersionByArnRequest {
  Arn: string | undefined;
}
export interface GetLayerVersionPolicyRequest {
  LayerName: string | undefined;
  VersionNumber: number | undefined;
}
export interface GetLayerVersionPolicyResponse {
  Policy?: string | undefined;
  RevisionId?: string | undefined;
}
export interface ListLayerVersionsRequest {
  CompatibleRuntime?: Runtime | undefined;
  LayerName: string | undefined;
  Marker?: string | undefined;
  MaxItems?: number | undefined;
  CompatibleArchitecture?: Architecture | undefined;
}
export interface ListLayerVersionsResponse {
  NextMarker?: string | undefined;
  LayerVersions?: LayerVersionsListItem[] | undefined;
}
export interface LayerVersionContentInput {
  S3Bucket?: string | undefined;
  S3Key?: string | undefined;
  S3ObjectVersion?: string | undefined;
  ZipFile?: Uint8Array | undefined;
}
export interface PublishLayerVersionRequest {
  LayerName: string | undefined;
  Description?: string | undefined;
  Content: LayerVersionContentInput | undefined;
  CompatibleRuntimes?: Runtime[] | undefined;
  LicenseInfo?: string | undefined;
  CompatibleArchitectures?: Architecture[] | undefined;
}
export interface PublishLayerVersionResponse {
  Content?: LayerVersionContentOutput | undefined;
  LayerArn?: string | undefined;
  LayerVersionArn?: string | undefined;
  Description?: string | undefined;
  CreatedDate?: string | undefined;
  Version?: number | undefined;
  CompatibleRuntimes?: Runtime[] | undefined;
  LicenseInfo?: string | undefined;
  CompatibleArchitectures?: Architecture[] | undefined;
}
export interface RemoveLayerVersionPermissionRequest {
  LayerName: string | undefined;
  VersionNumber: number | undefined;
  StatementId: string | undefined;
  RevisionId?: string | undefined;
}
export interface ListTagsRequest {
  Resource: string | undefined;
}
export interface ListTagsResponse {
  Tags?: Record<string, string> | undefined;
}
export interface RemovePermissionRequest {
  FunctionName: string | undefined;
  StatementId: string | undefined;
  Qualifier?: string | undefined;
  RevisionId?: string | undefined;
}
export interface DeleteProvisionedConcurrencyConfigRequest {
  FunctionName: string | undefined;
  Qualifier: string | undefined;
}
export interface GetProvisionedConcurrencyConfigRequest {
  FunctionName: string | undefined;
  Qualifier: string | undefined;
}
export interface GetProvisionedConcurrencyConfigResponse {
  RequestedProvisionedConcurrentExecutions?: number | undefined;
  AvailableProvisionedConcurrentExecutions?: number | undefined;
  AllocatedProvisionedConcurrentExecutions?: number | undefined;
  Status?: ProvisionedConcurrencyStatusEnum | undefined;
  StatusReason?: string | undefined;
  LastModified?: string | undefined;
}
export declare class ProvisionedConcurrencyConfigNotFoundException extends __BaseException {
  readonly name: "ProvisionedConcurrencyConfigNotFoundException";
  readonly $fault: "client";
  Type?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<
      ProvisionedConcurrencyConfigNotFoundException,
      __BaseException
    >
  );
}
export interface PutProvisionedConcurrencyConfigRequest {
  FunctionName: string | undefined;
  Qualifier: string | undefined;
  ProvisionedConcurrentExecutions: number | undefined;
}
export interface PutProvisionedConcurrencyConfigResponse {
  RequestedProvisionedConcurrentExecutions?: number | undefined;
  AvailableProvisionedConcurrentExecutions?: number | undefined;
  AllocatedProvisionedConcurrentExecutions?: number | undefined;
  Status?: ProvisionedConcurrencyStatusEnum | undefined;
  StatusReason?: string | undefined;
  LastModified?: string | undefined;
}
export interface TagResourceRequest {
  Resource: string | undefined;
  Tags: Record<string, string> | undefined;
}
export interface UntagResourceRequest {
  Resource: string | undefined;
  TagKeys: string[] | undefined;
}
export declare const FunctionCodeFilterSensitiveLog: (obj: FunctionCode) => any;
export declare const EnvironmentFilterSensitiveLog: (obj: Environment) => any;
export declare const CreateFunctionRequestFilterSensitiveLog: (
  obj: CreateFunctionRequest
) => any;
export declare const EnvironmentErrorFilterSensitiveLog: (
  obj: EnvironmentError
) => any;
export declare const EnvironmentResponseFilterSensitiveLog: (
  obj: EnvironmentResponse
) => any;
export declare const ImageConfigErrorFilterSensitiveLog: (
  obj: ImageConfigError
) => any;
export declare const ImageConfigResponseFilterSensitiveLog: (
  obj: ImageConfigResponse
) => any;
export declare const RuntimeVersionErrorFilterSensitiveLog: (
  obj: RuntimeVersionError
) => any;
export declare const RuntimeVersionConfigFilterSensitiveLog: (
  obj: RuntimeVersionConfig
) => any;
export declare const FunctionConfigurationFilterSensitiveLog: (
  obj: FunctionConfiguration
) => any;
export declare const GetFunctionResponseFilterSensitiveLog: (
  obj: GetFunctionResponse
) => any;
export declare const InvocationRequestFilterSensitiveLog: (
  obj: InvocationRequest
) => any;
export declare const InvocationResponseFilterSensitiveLog: (
  obj: InvocationResponse
) => any;
export declare const InvokeAsyncRequestFilterSensitiveLog: (
  obj: InvokeAsyncRequest
) => any;
export declare const InvokeWithResponseStreamRequestFilterSensitiveLog: (
  obj: InvokeWithResponseStreamRequest
) => any;
export declare const InvokeResponseStreamUpdateFilterSensitiveLog: (
  obj: InvokeResponseStreamUpdate
) => any;
export declare const InvokeWithResponseStreamResponseEventFilterSensitiveLog: (
  obj: InvokeWithResponseStreamResponseEvent
) => any;
export declare const InvokeWithResponseStreamResponseFilterSensitiveLog: (
  obj: InvokeWithResponseStreamResponse
) => any;
export declare const ListFunctionsResponseFilterSensitiveLog: (
  obj: ListFunctionsResponse
) => any;
export declare const UpdateFunctionCodeRequestFilterSensitiveLog: (
  obj: UpdateFunctionCodeRequest
) => any;
export declare const UpdateFunctionConfigurationRequestFilterSensitiveLog: (
  obj: UpdateFunctionConfigurationRequest
) => any;
export declare const ListVersionsByFunctionResponseFilterSensitiveLog: (
  obj: ListVersionsByFunctionResponse
) => any;
export declare const LayerVersionContentInputFilterSensitiveLog: (
  obj: LayerVersionContentInput
) => any;
export declare const PublishLayerVersionRequestFilterSensitiveLog: (
  obj: PublishLayerVersionRequest
) => any;
