Stack trace:
Frame         Function      Args
0007FFFFBF10  00021006118E (00021028DEE8, 000210272B3E, 0007FFFFBF10, 0007FFFFAE10) msys-2.0.dll+0x2118E
0007FFFFBF10  0002100469BA (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x69BA
0007FFFFBF10  0002100469F2 (00021028DF99, 0007FFFFBDC8, 0007FFFFBF10, 000000000000) msys-2.0.dll+0x69F2
0007FFFFBF10  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFFBF10  00021006A545 (0007FFFFBF20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFFBF20, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF90B840000 ntdll.dll
7FF90A880000 KERNEL32.DLL
7FF908DA0000 KERNELBASE.dll
7FF909E90000 USER32.dll
7FF9095B0000 win32u.dll
7FF9095F0000 GDI32.dll
7FF909470000 gdi32full.dll
7FF908CF0000 msvcp_win.dll
7FF908990000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF90A7C0000 advapi32.dll
7FF90AC60000 msvcrt.dll
7FF909720000 sechost.dll
7FF90AA40000 RPCRT4.dll
7FF907F40000 CRYPTBASE.DLL
7FF9093D0000 bcryptPrimitives.dll
7FF90A060000 IMM32.DLL
