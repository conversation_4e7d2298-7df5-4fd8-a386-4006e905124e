/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  transpilePackages: ['react-icons'],

  // CRITICAL FIX: Force metadata to appear in <head> instead of <body>
  // This overrides Next.js 15's streaming metadata behavior
  // According to official docs: "For HTML-limited bots that can't run JavaScript,
  // metadata continues to block page rendering and is placed in the <head> tag"
  htmlLimitedBots: /.*/,  // This forces ALL requests to use blocking metadata in <head>

  async rewrites() {
    return [
      {
        source: '/api/external/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
