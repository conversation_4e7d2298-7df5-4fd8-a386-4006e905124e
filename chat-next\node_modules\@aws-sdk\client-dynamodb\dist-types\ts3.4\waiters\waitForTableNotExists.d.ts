import { WaiterConfiguration, WaiterResult } from "@smithy/util-waiter";
import { DescribeTableCommandInput } from "../commands/DescribeTableCommand";
import { DynamoDBClient } from "../DynamoDBClient";
export declare const waitForTableNotExists: (
  params: WaiterConfiguration<DynamoDBClient>,
  input: DescribeTableCommandInput
) => Promise<WaiterResult>;
export declare const waitUntilTableNotExists: (
  params: WaiterConfiguration<DynamoDBClient>,
  input: DescribeTableCommandInput
) => Promise<WaiterResult>;
