import { HttpRequest as __HttpRequest, HttpResponse as __HttpResponse } from "@smithy/protocol-http";
import { SerdeContext as __SerdeContext } from "@smithy/types";
import { AssociateAliasCommandInput, AssociateAliasCommandOutput } from "../commands/AssociateAliasCommand";
import { CopyDistributionCommandInput, CopyDistributionCommandOutput } from "../commands/CopyDistributionCommand";
import { CreateCachePolicyCommandInput, CreateCachePolicyCommandOutput } from "../commands/CreateCachePolicyCommand";
import { CreateCloudFrontOriginAccessIdentityCommandInput, CreateCloudFrontOriginAccessIdentityCommandOutput } from "../commands/CreateCloudFrontOriginAccessIdentityCommand";
import { CreateContinuousDeploymentPolicyCommandInput, CreateContinuousDeploymentPolicyCommandOutput } from "../commands/CreateContinuousDeploymentPolicyCommand";
import { CreateDistributionCommandInput, CreateDistributionCommandOutput } from "../commands/CreateDistributionCommand";
import { CreateDistributionWithTagsCommandInput, CreateDistributionWithTagsCommandOutput } from "../commands/CreateDistributionWithTagsCommand";
import { CreateFieldLevelEncryptionConfigCommandInput, CreateFieldLevelEncryptionConfigCommandOutput } from "../commands/CreateFieldLevelEncryptionConfigCommand";
import { CreateFieldLevelEncryptionProfileCommandInput, CreateFieldLevelEncryptionProfileCommandOutput } from "../commands/CreateFieldLevelEncryptionProfileCommand";
import { CreateFunctionCommandInput, CreateFunctionCommandOutput } from "../commands/CreateFunctionCommand";
import { CreateInvalidationCommandInput, CreateInvalidationCommandOutput } from "../commands/CreateInvalidationCommand";
import { CreateKeyGroupCommandInput, CreateKeyGroupCommandOutput } from "../commands/CreateKeyGroupCommand";
import { CreateMonitoringSubscriptionCommandInput, CreateMonitoringSubscriptionCommandOutput } from "../commands/CreateMonitoringSubscriptionCommand";
import { CreateOriginAccessControlCommandInput, CreateOriginAccessControlCommandOutput } from "../commands/CreateOriginAccessControlCommand";
import { CreateOriginRequestPolicyCommandInput, CreateOriginRequestPolicyCommandOutput } from "../commands/CreateOriginRequestPolicyCommand";
import { CreatePublicKeyCommandInput, CreatePublicKeyCommandOutput } from "../commands/CreatePublicKeyCommand";
import { CreateRealtimeLogConfigCommandInput, CreateRealtimeLogConfigCommandOutput } from "../commands/CreateRealtimeLogConfigCommand";
import { CreateResponseHeadersPolicyCommandInput, CreateResponseHeadersPolicyCommandOutput } from "../commands/CreateResponseHeadersPolicyCommand";
import { CreateStreamingDistributionCommandInput, CreateStreamingDistributionCommandOutput } from "../commands/CreateStreamingDistributionCommand";
import { CreateStreamingDistributionWithTagsCommandInput, CreateStreamingDistributionWithTagsCommandOutput } from "../commands/CreateStreamingDistributionWithTagsCommand";
import { DeleteCachePolicyCommandInput, DeleteCachePolicyCommandOutput } from "../commands/DeleteCachePolicyCommand";
import { DeleteCloudFrontOriginAccessIdentityCommandInput, DeleteCloudFrontOriginAccessIdentityCommandOutput } from "../commands/DeleteCloudFrontOriginAccessIdentityCommand";
import { DeleteContinuousDeploymentPolicyCommandInput, DeleteContinuousDeploymentPolicyCommandOutput } from "../commands/DeleteContinuousDeploymentPolicyCommand";
import { DeleteDistributionCommandInput, DeleteDistributionCommandOutput } from "../commands/DeleteDistributionCommand";
import { DeleteFieldLevelEncryptionConfigCommandInput, DeleteFieldLevelEncryptionConfigCommandOutput } from "../commands/DeleteFieldLevelEncryptionConfigCommand";
import { DeleteFieldLevelEncryptionProfileCommandInput, DeleteFieldLevelEncryptionProfileCommandOutput } from "../commands/DeleteFieldLevelEncryptionProfileCommand";
import { DeleteFunctionCommandInput, DeleteFunctionCommandOutput } from "../commands/DeleteFunctionCommand";
import { DeleteKeyGroupCommandInput, DeleteKeyGroupCommandOutput } from "../commands/DeleteKeyGroupCommand";
import { DeleteMonitoringSubscriptionCommandInput, DeleteMonitoringSubscriptionCommandOutput } from "../commands/DeleteMonitoringSubscriptionCommand";
import { DeleteOriginAccessControlCommandInput, DeleteOriginAccessControlCommandOutput } from "../commands/DeleteOriginAccessControlCommand";
import { DeleteOriginRequestPolicyCommandInput, DeleteOriginRequestPolicyCommandOutput } from "../commands/DeleteOriginRequestPolicyCommand";
import { DeletePublicKeyCommandInput, DeletePublicKeyCommandOutput } from "../commands/DeletePublicKeyCommand";
import { DeleteRealtimeLogConfigCommandInput, DeleteRealtimeLogConfigCommandOutput } from "../commands/DeleteRealtimeLogConfigCommand";
import { DeleteResponseHeadersPolicyCommandInput, DeleteResponseHeadersPolicyCommandOutput } from "../commands/DeleteResponseHeadersPolicyCommand";
import { DeleteStreamingDistributionCommandInput, DeleteStreamingDistributionCommandOutput } from "../commands/DeleteStreamingDistributionCommand";
import { DescribeFunctionCommandInput, DescribeFunctionCommandOutput } from "../commands/DescribeFunctionCommand";
import { GetCachePolicyCommandInput, GetCachePolicyCommandOutput } from "../commands/GetCachePolicyCommand";
import { GetCachePolicyConfigCommandInput, GetCachePolicyConfigCommandOutput } from "../commands/GetCachePolicyConfigCommand";
import { GetCloudFrontOriginAccessIdentityCommandInput, GetCloudFrontOriginAccessIdentityCommandOutput } from "../commands/GetCloudFrontOriginAccessIdentityCommand";
import { GetCloudFrontOriginAccessIdentityConfigCommandInput, GetCloudFrontOriginAccessIdentityConfigCommandOutput } from "../commands/GetCloudFrontOriginAccessIdentityConfigCommand";
import { GetContinuousDeploymentPolicyCommandInput, GetContinuousDeploymentPolicyCommandOutput } from "../commands/GetContinuousDeploymentPolicyCommand";
import { GetContinuousDeploymentPolicyConfigCommandInput, GetContinuousDeploymentPolicyConfigCommandOutput } from "../commands/GetContinuousDeploymentPolicyConfigCommand";
import { GetDistributionCommandInput, GetDistributionCommandOutput } from "../commands/GetDistributionCommand";
import { GetDistributionConfigCommandInput, GetDistributionConfigCommandOutput } from "../commands/GetDistributionConfigCommand";
import { GetFieldLevelEncryptionCommandInput, GetFieldLevelEncryptionCommandOutput } from "../commands/GetFieldLevelEncryptionCommand";
import { GetFieldLevelEncryptionConfigCommandInput, GetFieldLevelEncryptionConfigCommandOutput } from "../commands/GetFieldLevelEncryptionConfigCommand";
import { GetFieldLevelEncryptionProfileCommandInput, GetFieldLevelEncryptionProfileCommandOutput } from "../commands/GetFieldLevelEncryptionProfileCommand";
import { GetFieldLevelEncryptionProfileConfigCommandInput, GetFieldLevelEncryptionProfileConfigCommandOutput } from "../commands/GetFieldLevelEncryptionProfileConfigCommand";
import { GetFunctionCommandInput, GetFunctionCommandOutput } from "../commands/GetFunctionCommand";
import { GetInvalidationCommandInput, GetInvalidationCommandOutput } from "../commands/GetInvalidationCommand";
import { GetKeyGroupCommandInput, GetKeyGroupCommandOutput } from "../commands/GetKeyGroupCommand";
import { GetKeyGroupConfigCommandInput, GetKeyGroupConfigCommandOutput } from "../commands/GetKeyGroupConfigCommand";
import { GetMonitoringSubscriptionCommandInput, GetMonitoringSubscriptionCommandOutput } from "../commands/GetMonitoringSubscriptionCommand";
import { GetOriginAccessControlCommandInput, GetOriginAccessControlCommandOutput } from "../commands/GetOriginAccessControlCommand";
import { GetOriginAccessControlConfigCommandInput, GetOriginAccessControlConfigCommandOutput } from "../commands/GetOriginAccessControlConfigCommand";
import { GetOriginRequestPolicyCommandInput, GetOriginRequestPolicyCommandOutput } from "../commands/GetOriginRequestPolicyCommand";
import { GetOriginRequestPolicyConfigCommandInput, GetOriginRequestPolicyConfigCommandOutput } from "../commands/GetOriginRequestPolicyConfigCommand";
import { GetPublicKeyCommandInput, GetPublicKeyCommandOutput } from "../commands/GetPublicKeyCommand";
import { GetPublicKeyConfigCommandInput, GetPublicKeyConfigCommandOutput } from "../commands/GetPublicKeyConfigCommand";
import { GetRealtimeLogConfigCommandInput, GetRealtimeLogConfigCommandOutput } from "../commands/GetRealtimeLogConfigCommand";
import { GetResponseHeadersPolicyCommandInput, GetResponseHeadersPolicyCommandOutput } from "../commands/GetResponseHeadersPolicyCommand";
import { GetResponseHeadersPolicyConfigCommandInput, GetResponseHeadersPolicyConfigCommandOutput } from "../commands/GetResponseHeadersPolicyConfigCommand";
import { GetStreamingDistributionCommandInput, GetStreamingDistributionCommandOutput } from "../commands/GetStreamingDistributionCommand";
import { GetStreamingDistributionConfigCommandInput, GetStreamingDistributionConfigCommandOutput } from "../commands/GetStreamingDistributionConfigCommand";
import { ListCachePoliciesCommandInput, ListCachePoliciesCommandOutput } from "../commands/ListCachePoliciesCommand";
import { ListCloudFrontOriginAccessIdentitiesCommandInput, ListCloudFrontOriginAccessIdentitiesCommandOutput } from "../commands/ListCloudFrontOriginAccessIdentitiesCommand";
import { ListConflictingAliasesCommandInput, ListConflictingAliasesCommandOutput } from "../commands/ListConflictingAliasesCommand";
import { ListContinuousDeploymentPoliciesCommandInput, ListContinuousDeploymentPoliciesCommandOutput } from "../commands/ListContinuousDeploymentPoliciesCommand";
import { ListDistributionsByCachePolicyIdCommandInput, ListDistributionsByCachePolicyIdCommandOutput } from "../commands/ListDistributionsByCachePolicyIdCommand";
import { ListDistributionsByKeyGroupCommandInput, ListDistributionsByKeyGroupCommandOutput } from "../commands/ListDistributionsByKeyGroupCommand";
import { ListDistributionsByOriginRequestPolicyIdCommandInput, ListDistributionsByOriginRequestPolicyIdCommandOutput } from "../commands/ListDistributionsByOriginRequestPolicyIdCommand";
import { ListDistributionsByRealtimeLogConfigCommandInput, ListDistributionsByRealtimeLogConfigCommandOutput } from "../commands/ListDistributionsByRealtimeLogConfigCommand";
import { ListDistributionsByResponseHeadersPolicyIdCommandInput, ListDistributionsByResponseHeadersPolicyIdCommandOutput } from "../commands/ListDistributionsByResponseHeadersPolicyIdCommand";
import { ListDistributionsByWebACLIdCommandInput, ListDistributionsByWebACLIdCommandOutput } from "../commands/ListDistributionsByWebACLIdCommand";
import { ListDistributionsCommandInput, ListDistributionsCommandOutput } from "../commands/ListDistributionsCommand";
import { ListFieldLevelEncryptionConfigsCommandInput, ListFieldLevelEncryptionConfigsCommandOutput } from "../commands/ListFieldLevelEncryptionConfigsCommand";
import { ListFieldLevelEncryptionProfilesCommandInput, ListFieldLevelEncryptionProfilesCommandOutput } from "../commands/ListFieldLevelEncryptionProfilesCommand";
import { ListFunctionsCommandInput, ListFunctionsCommandOutput } from "../commands/ListFunctionsCommand";
import { ListInvalidationsCommandInput, ListInvalidationsCommandOutput } from "../commands/ListInvalidationsCommand";
import { ListKeyGroupsCommandInput, ListKeyGroupsCommandOutput } from "../commands/ListKeyGroupsCommand";
import { ListOriginAccessControlsCommandInput, ListOriginAccessControlsCommandOutput } from "../commands/ListOriginAccessControlsCommand";
import { ListOriginRequestPoliciesCommandInput, ListOriginRequestPoliciesCommandOutput } from "../commands/ListOriginRequestPoliciesCommand";
import { ListPublicKeysCommandInput, ListPublicKeysCommandOutput } from "../commands/ListPublicKeysCommand";
import { ListRealtimeLogConfigsCommandInput, ListRealtimeLogConfigsCommandOutput } from "../commands/ListRealtimeLogConfigsCommand";
import { ListResponseHeadersPoliciesCommandInput, ListResponseHeadersPoliciesCommandOutput } from "../commands/ListResponseHeadersPoliciesCommand";
import { ListStreamingDistributionsCommandInput, ListStreamingDistributionsCommandOutput } from "../commands/ListStreamingDistributionsCommand";
import { ListTagsForResourceCommandInput, ListTagsForResourceCommandOutput } from "../commands/ListTagsForResourceCommand";
import { PublishFunctionCommandInput, PublishFunctionCommandOutput } from "../commands/PublishFunctionCommand";
import { TagResourceCommandInput, TagResourceCommandOutput } from "../commands/TagResourceCommand";
import { TestFunctionCommandInput, TestFunctionCommandOutput } from "../commands/TestFunctionCommand";
import { UntagResourceCommandInput, UntagResourceCommandOutput } from "../commands/UntagResourceCommand";
import { UpdateCachePolicyCommandInput, UpdateCachePolicyCommandOutput } from "../commands/UpdateCachePolicyCommand";
import { UpdateCloudFrontOriginAccessIdentityCommandInput, UpdateCloudFrontOriginAccessIdentityCommandOutput } from "../commands/UpdateCloudFrontOriginAccessIdentityCommand";
import { UpdateContinuousDeploymentPolicyCommandInput, UpdateContinuousDeploymentPolicyCommandOutput } from "../commands/UpdateContinuousDeploymentPolicyCommand";
import { UpdateDistributionCommandInput, UpdateDistributionCommandOutput } from "../commands/UpdateDistributionCommand";
import { UpdateDistributionWithStagingConfigCommandInput, UpdateDistributionWithStagingConfigCommandOutput } from "../commands/UpdateDistributionWithStagingConfigCommand";
import { UpdateFieldLevelEncryptionConfigCommandInput, UpdateFieldLevelEncryptionConfigCommandOutput } from "../commands/UpdateFieldLevelEncryptionConfigCommand";
import { UpdateFieldLevelEncryptionProfileCommandInput, UpdateFieldLevelEncryptionProfileCommandOutput } from "../commands/UpdateFieldLevelEncryptionProfileCommand";
import { UpdateFunctionCommandInput, UpdateFunctionCommandOutput } from "../commands/UpdateFunctionCommand";
import { UpdateKeyGroupCommandInput, UpdateKeyGroupCommandOutput } from "../commands/UpdateKeyGroupCommand";
import { UpdateOriginAccessControlCommandInput, UpdateOriginAccessControlCommandOutput } from "../commands/UpdateOriginAccessControlCommand";
import { UpdateOriginRequestPolicyCommandInput, UpdateOriginRequestPolicyCommandOutput } from "../commands/UpdateOriginRequestPolicyCommand";
import { UpdatePublicKeyCommandInput, UpdatePublicKeyCommandOutput } from "../commands/UpdatePublicKeyCommand";
import { UpdateRealtimeLogConfigCommandInput, UpdateRealtimeLogConfigCommandOutput } from "../commands/UpdateRealtimeLogConfigCommand";
import { UpdateResponseHeadersPolicyCommandInput, UpdateResponseHeadersPolicyCommandOutput } from "../commands/UpdateResponseHeadersPolicyCommand";
import { UpdateStreamingDistributionCommandInput, UpdateStreamingDistributionCommandOutput } from "../commands/UpdateStreamingDistributionCommand";
/**
 * serializeAws_restXmlAssociateAliasCommand
 */
export declare const se_AssociateAliasCommand: (input: AssociateAliasCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCopyDistributionCommand
 */
export declare const se_CopyDistributionCommand: (input: CopyDistributionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateCachePolicyCommand
 */
export declare const se_CreateCachePolicyCommand: (input: CreateCachePolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateCloudFrontOriginAccessIdentityCommand
 */
export declare const se_CreateCloudFrontOriginAccessIdentityCommand: (input: CreateCloudFrontOriginAccessIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateContinuousDeploymentPolicyCommand
 */
export declare const se_CreateContinuousDeploymentPolicyCommand: (input: CreateContinuousDeploymentPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateDistributionCommand
 */
export declare const se_CreateDistributionCommand: (input: CreateDistributionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateDistributionWithTagsCommand
 */
export declare const se_CreateDistributionWithTagsCommand: (input: CreateDistributionWithTagsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateFieldLevelEncryptionConfigCommand
 */
export declare const se_CreateFieldLevelEncryptionConfigCommand: (input: CreateFieldLevelEncryptionConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateFieldLevelEncryptionProfileCommand
 */
export declare const se_CreateFieldLevelEncryptionProfileCommand: (input: CreateFieldLevelEncryptionProfileCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateFunctionCommand
 */
export declare const se_CreateFunctionCommand: (input: CreateFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateInvalidationCommand
 */
export declare const se_CreateInvalidationCommand: (input: CreateInvalidationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateKeyGroupCommand
 */
export declare const se_CreateKeyGroupCommand: (input: CreateKeyGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateMonitoringSubscriptionCommand
 */
export declare const se_CreateMonitoringSubscriptionCommand: (input: CreateMonitoringSubscriptionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateOriginAccessControlCommand
 */
export declare const se_CreateOriginAccessControlCommand: (input: CreateOriginAccessControlCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateOriginRequestPolicyCommand
 */
export declare const se_CreateOriginRequestPolicyCommand: (input: CreateOriginRequestPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreatePublicKeyCommand
 */
export declare const se_CreatePublicKeyCommand: (input: CreatePublicKeyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateRealtimeLogConfigCommand
 */
export declare const se_CreateRealtimeLogConfigCommand: (input: CreateRealtimeLogConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateResponseHeadersPolicyCommand
 */
export declare const se_CreateResponseHeadersPolicyCommand: (input: CreateResponseHeadersPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateStreamingDistributionCommand
 */
export declare const se_CreateStreamingDistributionCommand: (input: CreateStreamingDistributionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlCreateStreamingDistributionWithTagsCommand
 */
export declare const se_CreateStreamingDistributionWithTagsCommand: (input: CreateStreamingDistributionWithTagsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteCachePolicyCommand
 */
export declare const se_DeleteCachePolicyCommand: (input: DeleteCachePolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteCloudFrontOriginAccessIdentityCommand
 */
export declare const se_DeleteCloudFrontOriginAccessIdentityCommand: (input: DeleteCloudFrontOriginAccessIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteContinuousDeploymentPolicyCommand
 */
export declare const se_DeleteContinuousDeploymentPolicyCommand: (input: DeleteContinuousDeploymentPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteDistributionCommand
 */
export declare const se_DeleteDistributionCommand: (input: DeleteDistributionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteFieldLevelEncryptionConfigCommand
 */
export declare const se_DeleteFieldLevelEncryptionConfigCommand: (input: DeleteFieldLevelEncryptionConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteFieldLevelEncryptionProfileCommand
 */
export declare const se_DeleteFieldLevelEncryptionProfileCommand: (input: DeleteFieldLevelEncryptionProfileCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteFunctionCommand
 */
export declare const se_DeleteFunctionCommand: (input: DeleteFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteKeyGroupCommand
 */
export declare const se_DeleteKeyGroupCommand: (input: DeleteKeyGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteMonitoringSubscriptionCommand
 */
export declare const se_DeleteMonitoringSubscriptionCommand: (input: DeleteMonitoringSubscriptionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteOriginAccessControlCommand
 */
export declare const se_DeleteOriginAccessControlCommand: (input: DeleteOriginAccessControlCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteOriginRequestPolicyCommand
 */
export declare const se_DeleteOriginRequestPolicyCommand: (input: DeleteOriginRequestPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeletePublicKeyCommand
 */
export declare const se_DeletePublicKeyCommand: (input: DeletePublicKeyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteRealtimeLogConfigCommand
 */
export declare const se_DeleteRealtimeLogConfigCommand: (input: DeleteRealtimeLogConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteResponseHeadersPolicyCommand
 */
export declare const se_DeleteResponseHeadersPolicyCommand: (input: DeleteResponseHeadersPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDeleteStreamingDistributionCommand
 */
export declare const se_DeleteStreamingDistributionCommand: (input: DeleteStreamingDistributionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlDescribeFunctionCommand
 */
export declare const se_DescribeFunctionCommand: (input: DescribeFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetCachePolicyCommand
 */
export declare const se_GetCachePolicyCommand: (input: GetCachePolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetCachePolicyConfigCommand
 */
export declare const se_GetCachePolicyConfigCommand: (input: GetCachePolicyConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetCloudFrontOriginAccessIdentityCommand
 */
export declare const se_GetCloudFrontOriginAccessIdentityCommand: (input: GetCloudFrontOriginAccessIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetCloudFrontOriginAccessIdentityConfigCommand
 */
export declare const se_GetCloudFrontOriginAccessIdentityConfigCommand: (input: GetCloudFrontOriginAccessIdentityConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetContinuousDeploymentPolicyCommand
 */
export declare const se_GetContinuousDeploymentPolicyCommand: (input: GetContinuousDeploymentPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetContinuousDeploymentPolicyConfigCommand
 */
export declare const se_GetContinuousDeploymentPolicyConfigCommand: (input: GetContinuousDeploymentPolicyConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetDistributionCommand
 */
export declare const se_GetDistributionCommand: (input: GetDistributionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetDistributionConfigCommand
 */
export declare const se_GetDistributionConfigCommand: (input: GetDistributionConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetFieldLevelEncryptionCommand
 */
export declare const se_GetFieldLevelEncryptionCommand: (input: GetFieldLevelEncryptionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetFieldLevelEncryptionConfigCommand
 */
export declare const se_GetFieldLevelEncryptionConfigCommand: (input: GetFieldLevelEncryptionConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetFieldLevelEncryptionProfileCommand
 */
export declare const se_GetFieldLevelEncryptionProfileCommand: (input: GetFieldLevelEncryptionProfileCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetFieldLevelEncryptionProfileConfigCommand
 */
export declare const se_GetFieldLevelEncryptionProfileConfigCommand: (input: GetFieldLevelEncryptionProfileConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetFunctionCommand
 */
export declare const se_GetFunctionCommand: (input: GetFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetInvalidationCommand
 */
export declare const se_GetInvalidationCommand: (input: GetInvalidationCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetKeyGroupCommand
 */
export declare const se_GetKeyGroupCommand: (input: GetKeyGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetKeyGroupConfigCommand
 */
export declare const se_GetKeyGroupConfigCommand: (input: GetKeyGroupConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetMonitoringSubscriptionCommand
 */
export declare const se_GetMonitoringSubscriptionCommand: (input: GetMonitoringSubscriptionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetOriginAccessControlCommand
 */
export declare const se_GetOriginAccessControlCommand: (input: GetOriginAccessControlCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetOriginAccessControlConfigCommand
 */
export declare const se_GetOriginAccessControlConfigCommand: (input: GetOriginAccessControlConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetOriginRequestPolicyCommand
 */
export declare const se_GetOriginRequestPolicyCommand: (input: GetOriginRequestPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetOriginRequestPolicyConfigCommand
 */
export declare const se_GetOriginRequestPolicyConfigCommand: (input: GetOriginRequestPolicyConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetPublicKeyCommand
 */
export declare const se_GetPublicKeyCommand: (input: GetPublicKeyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetPublicKeyConfigCommand
 */
export declare const se_GetPublicKeyConfigCommand: (input: GetPublicKeyConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetRealtimeLogConfigCommand
 */
export declare const se_GetRealtimeLogConfigCommand: (input: GetRealtimeLogConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetResponseHeadersPolicyCommand
 */
export declare const se_GetResponseHeadersPolicyCommand: (input: GetResponseHeadersPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetResponseHeadersPolicyConfigCommand
 */
export declare const se_GetResponseHeadersPolicyConfigCommand: (input: GetResponseHeadersPolicyConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetStreamingDistributionCommand
 */
export declare const se_GetStreamingDistributionCommand: (input: GetStreamingDistributionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlGetStreamingDistributionConfigCommand
 */
export declare const se_GetStreamingDistributionConfigCommand: (input: GetStreamingDistributionConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListCachePoliciesCommand
 */
export declare const se_ListCachePoliciesCommand: (input: ListCachePoliciesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListCloudFrontOriginAccessIdentitiesCommand
 */
export declare const se_ListCloudFrontOriginAccessIdentitiesCommand: (input: ListCloudFrontOriginAccessIdentitiesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListConflictingAliasesCommand
 */
export declare const se_ListConflictingAliasesCommand: (input: ListConflictingAliasesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListContinuousDeploymentPoliciesCommand
 */
export declare const se_ListContinuousDeploymentPoliciesCommand: (input: ListContinuousDeploymentPoliciesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListDistributionsCommand
 */
export declare const se_ListDistributionsCommand: (input: ListDistributionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListDistributionsByCachePolicyIdCommand
 */
export declare const se_ListDistributionsByCachePolicyIdCommand: (input: ListDistributionsByCachePolicyIdCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListDistributionsByKeyGroupCommand
 */
export declare const se_ListDistributionsByKeyGroupCommand: (input: ListDistributionsByKeyGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListDistributionsByOriginRequestPolicyIdCommand
 */
export declare const se_ListDistributionsByOriginRequestPolicyIdCommand: (input: ListDistributionsByOriginRequestPolicyIdCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListDistributionsByRealtimeLogConfigCommand
 */
export declare const se_ListDistributionsByRealtimeLogConfigCommand: (input: ListDistributionsByRealtimeLogConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListDistributionsByResponseHeadersPolicyIdCommand
 */
export declare const se_ListDistributionsByResponseHeadersPolicyIdCommand: (input: ListDistributionsByResponseHeadersPolicyIdCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListDistributionsByWebACLIdCommand
 */
export declare const se_ListDistributionsByWebACLIdCommand: (input: ListDistributionsByWebACLIdCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListFieldLevelEncryptionConfigsCommand
 */
export declare const se_ListFieldLevelEncryptionConfigsCommand: (input: ListFieldLevelEncryptionConfigsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListFieldLevelEncryptionProfilesCommand
 */
export declare const se_ListFieldLevelEncryptionProfilesCommand: (input: ListFieldLevelEncryptionProfilesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListFunctionsCommand
 */
export declare const se_ListFunctionsCommand: (input: ListFunctionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListInvalidationsCommand
 */
export declare const se_ListInvalidationsCommand: (input: ListInvalidationsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListKeyGroupsCommand
 */
export declare const se_ListKeyGroupsCommand: (input: ListKeyGroupsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListOriginAccessControlsCommand
 */
export declare const se_ListOriginAccessControlsCommand: (input: ListOriginAccessControlsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListOriginRequestPoliciesCommand
 */
export declare const se_ListOriginRequestPoliciesCommand: (input: ListOriginRequestPoliciesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListPublicKeysCommand
 */
export declare const se_ListPublicKeysCommand: (input: ListPublicKeysCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListRealtimeLogConfigsCommand
 */
export declare const se_ListRealtimeLogConfigsCommand: (input: ListRealtimeLogConfigsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListResponseHeadersPoliciesCommand
 */
export declare const se_ListResponseHeadersPoliciesCommand: (input: ListResponseHeadersPoliciesCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListStreamingDistributionsCommand
 */
export declare const se_ListStreamingDistributionsCommand: (input: ListStreamingDistributionsCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlListTagsForResourceCommand
 */
export declare const se_ListTagsForResourceCommand: (input: ListTagsForResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlPublishFunctionCommand
 */
export declare const se_PublishFunctionCommand: (input: PublishFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlTagResourceCommand
 */
export declare const se_TagResourceCommand: (input: TagResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlTestFunctionCommand
 */
export declare const se_TestFunctionCommand: (input: TestFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUntagResourceCommand
 */
export declare const se_UntagResourceCommand: (input: UntagResourceCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateCachePolicyCommand
 */
export declare const se_UpdateCachePolicyCommand: (input: UpdateCachePolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateCloudFrontOriginAccessIdentityCommand
 */
export declare const se_UpdateCloudFrontOriginAccessIdentityCommand: (input: UpdateCloudFrontOriginAccessIdentityCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateContinuousDeploymentPolicyCommand
 */
export declare const se_UpdateContinuousDeploymentPolicyCommand: (input: UpdateContinuousDeploymentPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateDistributionCommand
 */
export declare const se_UpdateDistributionCommand: (input: UpdateDistributionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateDistributionWithStagingConfigCommand
 */
export declare const se_UpdateDistributionWithStagingConfigCommand: (input: UpdateDistributionWithStagingConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateFieldLevelEncryptionConfigCommand
 */
export declare const se_UpdateFieldLevelEncryptionConfigCommand: (input: UpdateFieldLevelEncryptionConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateFieldLevelEncryptionProfileCommand
 */
export declare const se_UpdateFieldLevelEncryptionProfileCommand: (input: UpdateFieldLevelEncryptionProfileCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateFunctionCommand
 */
export declare const se_UpdateFunctionCommand: (input: UpdateFunctionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateKeyGroupCommand
 */
export declare const se_UpdateKeyGroupCommand: (input: UpdateKeyGroupCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateOriginAccessControlCommand
 */
export declare const se_UpdateOriginAccessControlCommand: (input: UpdateOriginAccessControlCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateOriginRequestPolicyCommand
 */
export declare const se_UpdateOriginRequestPolicyCommand: (input: UpdateOriginRequestPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdatePublicKeyCommand
 */
export declare const se_UpdatePublicKeyCommand: (input: UpdatePublicKeyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateRealtimeLogConfigCommand
 */
export declare const se_UpdateRealtimeLogConfigCommand: (input: UpdateRealtimeLogConfigCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateResponseHeadersPolicyCommand
 */
export declare const se_UpdateResponseHeadersPolicyCommand: (input: UpdateResponseHeadersPolicyCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * serializeAws_restXmlUpdateStreamingDistributionCommand
 */
export declare const se_UpdateStreamingDistributionCommand: (input: UpdateStreamingDistributionCommandInput, context: __SerdeContext) => Promise<__HttpRequest>;
/**
 * deserializeAws_restXmlAssociateAliasCommand
 */
export declare const de_AssociateAliasCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<AssociateAliasCommandOutput>;
/**
 * deserializeAws_restXmlCopyDistributionCommand
 */
export declare const de_CopyDistributionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CopyDistributionCommandOutput>;
/**
 * deserializeAws_restXmlCreateCachePolicyCommand
 */
export declare const de_CreateCachePolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateCachePolicyCommandOutput>;
/**
 * deserializeAws_restXmlCreateCloudFrontOriginAccessIdentityCommand
 */
export declare const de_CreateCloudFrontOriginAccessIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateCloudFrontOriginAccessIdentityCommandOutput>;
/**
 * deserializeAws_restXmlCreateContinuousDeploymentPolicyCommand
 */
export declare const de_CreateContinuousDeploymentPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateContinuousDeploymentPolicyCommandOutput>;
/**
 * deserializeAws_restXmlCreateDistributionCommand
 */
export declare const de_CreateDistributionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateDistributionCommandOutput>;
/**
 * deserializeAws_restXmlCreateDistributionWithTagsCommand
 */
export declare const de_CreateDistributionWithTagsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateDistributionWithTagsCommandOutput>;
/**
 * deserializeAws_restXmlCreateFieldLevelEncryptionConfigCommand
 */
export declare const de_CreateFieldLevelEncryptionConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateFieldLevelEncryptionConfigCommandOutput>;
/**
 * deserializeAws_restXmlCreateFieldLevelEncryptionProfileCommand
 */
export declare const de_CreateFieldLevelEncryptionProfileCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateFieldLevelEncryptionProfileCommandOutput>;
/**
 * deserializeAws_restXmlCreateFunctionCommand
 */
export declare const de_CreateFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateFunctionCommandOutput>;
/**
 * deserializeAws_restXmlCreateInvalidationCommand
 */
export declare const de_CreateInvalidationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateInvalidationCommandOutput>;
/**
 * deserializeAws_restXmlCreateKeyGroupCommand
 */
export declare const de_CreateKeyGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateKeyGroupCommandOutput>;
/**
 * deserializeAws_restXmlCreateMonitoringSubscriptionCommand
 */
export declare const de_CreateMonitoringSubscriptionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateMonitoringSubscriptionCommandOutput>;
/**
 * deserializeAws_restXmlCreateOriginAccessControlCommand
 */
export declare const de_CreateOriginAccessControlCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateOriginAccessControlCommandOutput>;
/**
 * deserializeAws_restXmlCreateOriginRequestPolicyCommand
 */
export declare const de_CreateOriginRequestPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateOriginRequestPolicyCommandOutput>;
/**
 * deserializeAws_restXmlCreatePublicKeyCommand
 */
export declare const de_CreatePublicKeyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreatePublicKeyCommandOutput>;
/**
 * deserializeAws_restXmlCreateRealtimeLogConfigCommand
 */
export declare const de_CreateRealtimeLogConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateRealtimeLogConfigCommandOutput>;
/**
 * deserializeAws_restXmlCreateResponseHeadersPolicyCommand
 */
export declare const de_CreateResponseHeadersPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateResponseHeadersPolicyCommandOutput>;
/**
 * deserializeAws_restXmlCreateStreamingDistributionCommand
 */
export declare const de_CreateStreamingDistributionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateStreamingDistributionCommandOutput>;
/**
 * deserializeAws_restXmlCreateStreamingDistributionWithTagsCommand
 */
export declare const de_CreateStreamingDistributionWithTagsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<CreateStreamingDistributionWithTagsCommandOutput>;
/**
 * deserializeAws_restXmlDeleteCachePolicyCommand
 */
export declare const de_DeleteCachePolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteCachePolicyCommandOutput>;
/**
 * deserializeAws_restXmlDeleteCloudFrontOriginAccessIdentityCommand
 */
export declare const de_DeleteCloudFrontOriginAccessIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteCloudFrontOriginAccessIdentityCommandOutput>;
/**
 * deserializeAws_restXmlDeleteContinuousDeploymentPolicyCommand
 */
export declare const de_DeleteContinuousDeploymentPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteContinuousDeploymentPolicyCommandOutput>;
/**
 * deserializeAws_restXmlDeleteDistributionCommand
 */
export declare const de_DeleteDistributionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteDistributionCommandOutput>;
/**
 * deserializeAws_restXmlDeleteFieldLevelEncryptionConfigCommand
 */
export declare const de_DeleteFieldLevelEncryptionConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteFieldLevelEncryptionConfigCommandOutput>;
/**
 * deserializeAws_restXmlDeleteFieldLevelEncryptionProfileCommand
 */
export declare const de_DeleteFieldLevelEncryptionProfileCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteFieldLevelEncryptionProfileCommandOutput>;
/**
 * deserializeAws_restXmlDeleteFunctionCommand
 */
export declare const de_DeleteFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteFunctionCommandOutput>;
/**
 * deserializeAws_restXmlDeleteKeyGroupCommand
 */
export declare const de_DeleteKeyGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteKeyGroupCommandOutput>;
/**
 * deserializeAws_restXmlDeleteMonitoringSubscriptionCommand
 */
export declare const de_DeleteMonitoringSubscriptionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteMonitoringSubscriptionCommandOutput>;
/**
 * deserializeAws_restXmlDeleteOriginAccessControlCommand
 */
export declare const de_DeleteOriginAccessControlCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteOriginAccessControlCommandOutput>;
/**
 * deserializeAws_restXmlDeleteOriginRequestPolicyCommand
 */
export declare const de_DeleteOriginRequestPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteOriginRequestPolicyCommandOutput>;
/**
 * deserializeAws_restXmlDeletePublicKeyCommand
 */
export declare const de_DeletePublicKeyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeletePublicKeyCommandOutput>;
/**
 * deserializeAws_restXmlDeleteRealtimeLogConfigCommand
 */
export declare const de_DeleteRealtimeLogConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteRealtimeLogConfigCommandOutput>;
/**
 * deserializeAws_restXmlDeleteResponseHeadersPolicyCommand
 */
export declare const de_DeleteResponseHeadersPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteResponseHeadersPolicyCommandOutput>;
/**
 * deserializeAws_restXmlDeleteStreamingDistributionCommand
 */
export declare const de_DeleteStreamingDistributionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DeleteStreamingDistributionCommandOutput>;
/**
 * deserializeAws_restXmlDescribeFunctionCommand
 */
export declare const de_DescribeFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<DescribeFunctionCommandOutput>;
/**
 * deserializeAws_restXmlGetCachePolicyCommand
 */
export declare const de_GetCachePolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetCachePolicyCommandOutput>;
/**
 * deserializeAws_restXmlGetCachePolicyConfigCommand
 */
export declare const de_GetCachePolicyConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetCachePolicyConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetCloudFrontOriginAccessIdentityCommand
 */
export declare const de_GetCloudFrontOriginAccessIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetCloudFrontOriginAccessIdentityCommandOutput>;
/**
 * deserializeAws_restXmlGetCloudFrontOriginAccessIdentityConfigCommand
 */
export declare const de_GetCloudFrontOriginAccessIdentityConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetCloudFrontOriginAccessIdentityConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetContinuousDeploymentPolicyCommand
 */
export declare const de_GetContinuousDeploymentPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetContinuousDeploymentPolicyCommandOutput>;
/**
 * deserializeAws_restXmlGetContinuousDeploymentPolicyConfigCommand
 */
export declare const de_GetContinuousDeploymentPolicyConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetContinuousDeploymentPolicyConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetDistributionCommand
 */
export declare const de_GetDistributionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDistributionCommandOutput>;
/**
 * deserializeAws_restXmlGetDistributionConfigCommand
 */
export declare const de_GetDistributionConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetDistributionConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetFieldLevelEncryptionCommand
 */
export declare const de_GetFieldLevelEncryptionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetFieldLevelEncryptionCommandOutput>;
/**
 * deserializeAws_restXmlGetFieldLevelEncryptionConfigCommand
 */
export declare const de_GetFieldLevelEncryptionConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetFieldLevelEncryptionConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetFieldLevelEncryptionProfileCommand
 */
export declare const de_GetFieldLevelEncryptionProfileCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetFieldLevelEncryptionProfileCommandOutput>;
/**
 * deserializeAws_restXmlGetFieldLevelEncryptionProfileConfigCommand
 */
export declare const de_GetFieldLevelEncryptionProfileConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetFieldLevelEncryptionProfileConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetFunctionCommand
 */
export declare const de_GetFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetFunctionCommandOutput>;
/**
 * deserializeAws_restXmlGetInvalidationCommand
 */
export declare const de_GetInvalidationCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetInvalidationCommandOutput>;
/**
 * deserializeAws_restXmlGetKeyGroupCommand
 */
export declare const de_GetKeyGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetKeyGroupCommandOutput>;
/**
 * deserializeAws_restXmlGetKeyGroupConfigCommand
 */
export declare const de_GetKeyGroupConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetKeyGroupConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetMonitoringSubscriptionCommand
 */
export declare const de_GetMonitoringSubscriptionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetMonitoringSubscriptionCommandOutput>;
/**
 * deserializeAws_restXmlGetOriginAccessControlCommand
 */
export declare const de_GetOriginAccessControlCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetOriginAccessControlCommandOutput>;
/**
 * deserializeAws_restXmlGetOriginAccessControlConfigCommand
 */
export declare const de_GetOriginAccessControlConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetOriginAccessControlConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetOriginRequestPolicyCommand
 */
export declare const de_GetOriginRequestPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetOriginRequestPolicyCommandOutput>;
/**
 * deserializeAws_restXmlGetOriginRequestPolicyConfigCommand
 */
export declare const de_GetOriginRequestPolicyConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetOriginRequestPolicyConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetPublicKeyCommand
 */
export declare const de_GetPublicKeyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetPublicKeyCommandOutput>;
/**
 * deserializeAws_restXmlGetPublicKeyConfigCommand
 */
export declare const de_GetPublicKeyConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetPublicKeyConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetRealtimeLogConfigCommand
 */
export declare const de_GetRealtimeLogConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetRealtimeLogConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetResponseHeadersPolicyCommand
 */
export declare const de_GetResponseHeadersPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetResponseHeadersPolicyCommandOutput>;
/**
 * deserializeAws_restXmlGetResponseHeadersPolicyConfigCommand
 */
export declare const de_GetResponseHeadersPolicyConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetResponseHeadersPolicyConfigCommandOutput>;
/**
 * deserializeAws_restXmlGetStreamingDistributionCommand
 */
export declare const de_GetStreamingDistributionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetStreamingDistributionCommandOutput>;
/**
 * deserializeAws_restXmlGetStreamingDistributionConfigCommand
 */
export declare const de_GetStreamingDistributionConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<GetStreamingDistributionConfigCommandOutput>;
/**
 * deserializeAws_restXmlListCachePoliciesCommand
 */
export declare const de_ListCachePoliciesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListCachePoliciesCommandOutput>;
/**
 * deserializeAws_restXmlListCloudFrontOriginAccessIdentitiesCommand
 */
export declare const de_ListCloudFrontOriginAccessIdentitiesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListCloudFrontOriginAccessIdentitiesCommandOutput>;
/**
 * deserializeAws_restXmlListConflictingAliasesCommand
 */
export declare const de_ListConflictingAliasesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListConflictingAliasesCommandOutput>;
/**
 * deserializeAws_restXmlListContinuousDeploymentPoliciesCommand
 */
export declare const de_ListContinuousDeploymentPoliciesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListContinuousDeploymentPoliciesCommandOutput>;
/**
 * deserializeAws_restXmlListDistributionsCommand
 */
export declare const de_ListDistributionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDistributionsCommandOutput>;
/**
 * deserializeAws_restXmlListDistributionsByCachePolicyIdCommand
 */
export declare const de_ListDistributionsByCachePolicyIdCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDistributionsByCachePolicyIdCommandOutput>;
/**
 * deserializeAws_restXmlListDistributionsByKeyGroupCommand
 */
export declare const de_ListDistributionsByKeyGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDistributionsByKeyGroupCommandOutput>;
/**
 * deserializeAws_restXmlListDistributionsByOriginRequestPolicyIdCommand
 */
export declare const de_ListDistributionsByOriginRequestPolicyIdCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDistributionsByOriginRequestPolicyIdCommandOutput>;
/**
 * deserializeAws_restXmlListDistributionsByRealtimeLogConfigCommand
 */
export declare const de_ListDistributionsByRealtimeLogConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDistributionsByRealtimeLogConfigCommandOutput>;
/**
 * deserializeAws_restXmlListDistributionsByResponseHeadersPolicyIdCommand
 */
export declare const de_ListDistributionsByResponseHeadersPolicyIdCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDistributionsByResponseHeadersPolicyIdCommandOutput>;
/**
 * deserializeAws_restXmlListDistributionsByWebACLIdCommand
 */
export declare const de_ListDistributionsByWebACLIdCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListDistributionsByWebACLIdCommandOutput>;
/**
 * deserializeAws_restXmlListFieldLevelEncryptionConfigsCommand
 */
export declare const de_ListFieldLevelEncryptionConfigsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListFieldLevelEncryptionConfigsCommandOutput>;
/**
 * deserializeAws_restXmlListFieldLevelEncryptionProfilesCommand
 */
export declare const de_ListFieldLevelEncryptionProfilesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListFieldLevelEncryptionProfilesCommandOutput>;
/**
 * deserializeAws_restXmlListFunctionsCommand
 */
export declare const de_ListFunctionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListFunctionsCommandOutput>;
/**
 * deserializeAws_restXmlListInvalidationsCommand
 */
export declare const de_ListInvalidationsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListInvalidationsCommandOutput>;
/**
 * deserializeAws_restXmlListKeyGroupsCommand
 */
export declare const de_ListKeyGroupsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListKeyGroupsCommandOutput>;
/**
 * deserializeAws_restXmlListOriginAccessControlsCommand
 */
export declare const de_ListOriginAccessControlsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListOriginAccessControlsCommandOutput>;
/**
 * deserializeAws_restXmlListOriginRequestPoliciesCommand
 */
export declare const de_ListOriginRequestPoliciesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListOriginRequestPoliciesCommandOutput>;
/**
 * deserializeAws_restXmlListPublicKeysCommand
 */
export declare const de_ListPublicKeysCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListPublicKeysCommandOutput>;
/**
 * deserializeAws_restXmlListRealtimeLogConfigsCommand
 */
export declare const de_ListRealtimeLogConfigsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListRealtimeLogConfigsCommandOutput>;
/**
 * deserializeAws_restXmlListResponseHeadersPoliciesCommand
 */
export declare const de_ListResponseHeadersPoliciesCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListResponseHeadersPoliciesCommandOutput>;
/**
 * deserializeAws_restXmlListStreamingDistributionsCommand
 */
export declare const de_ListStreamingDistributionsCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListStreamingDistributionsCommandOutput>;
/**
 * deserializeAws_restXmlListTagsForResourceCommand
 */
export declare const de_ListTagsForResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<ListTagsForResourceCommandOutput>;
/**
 * deserializeAws_restXmlPublishFunctionCommand
 */
export declare const de_PublishFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<PublishFunctionCommandOutput>;
/**
 * deserializeAws_restXmlTagResourceCommand
 */
export declare const de_TagResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<TagResourceCommandOutput>;
/**
 * deserializeAws_restXmlTestFunctionCommand
 */
export declare const de_TestFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<TestFunctionCommandOutput>;
/**
 * deserializeAws_restXmlUntagResourceCommand
 */
export declare const de_UntagResourceCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UntagResourceCommandOutput>;
/**
 * deserializeAws_restXmlUpdateCachePolicyCommand
 */
export declare const de_UpdateCachePolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateCachePolicyCommandOutput>;
/**
 * deserializeAws_restXmlUpdateCloudFrontOriginAccessIdentityCommand
 */
export declare const de_UpdateCloudFrontOriginAccessIdentityCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateCloudFrontOriginAccessIdentityCommandOutput>;
/**
 * deserializeAws_restXmlUpdateContinuousDeploymentPolicyCommand
 */
export declare const de_UpdateContinuousDeploymentPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateContinuousDeploymentPolicyCommandOutput>;
/**
 * deserializeAws_restXmlUpdateDistributionCommand
 */
export declare const de_UpdateDistributionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateDistributionCommandOutput>;
/**
 * deserializeAws_restXmlUpdateDistributionWithStagingConfigCommand
 */
export declare const de_UpdateDistributionWithStagingConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateDistributionWithStagingConfigCommandOutput>;
/**
 * deserializeAws_restXmlUpdateFieldLevelEncryptionConfigCommand
 */
export declare const de_UpdateFieldLevelEncryptionConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateFieldLevelEncryptionConfigCommandOutput>;
/**
 * deserializeAws_restXmlUpdateFieldLevelEncryptionProfileCommand
 */
export declare const de_UpdateFieldLevelEncryptionProfileCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateFieldLevelEncryptionProfileCommandOutput>;
/**
 * deserializeAws_restXmlUpdateFunctionCommand
 */
export declare const de_UpdateFunctionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateFunctionCommandOutput>;
/**
 * deserializeAws_restXmlUpdateKeyGroupCommand
 */
export declare const de_UpdateKeyGroupCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateKeyGroupCommandOutput>;
/**
 * deserializeAws_restXmlUpdateOriginAccessControlCommand
 */
export declare const de_UpdateOriginAccessControlCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateOriginAccessControlCommandOutput>;
/**
 * deserializeAws_restXmlUpdateOriginRequestPolicyCommand
 */
export declare const de_UpdateOriginRequestPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateOriginRequestPolicyCommandOutput>;
/**
 * deserializeAws_restXmlUpdatePublicKeyCommand
 */
export declare const de_UpdatePublicKeyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdatePublicKeyCommandOutput>;
/**
 * deserializeAws_restXmlUpdateRealtimeLogConfigCommand
 */
export declare const de_UpdateRealtimeLogConfigCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateRealtimeLogConfigCommandOutput>;
/**
 * deserializeAws_restXmlUpdateResponseHeadersPolicyCommand
 */
export declare const de_UpdateResponseHeadersPolicyCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateResponseHeadersPolicyCommandOutput>;
/**
 * deserializeAws_restXmlUpdateStreamingDistributionCommand
 */
export declare const de_UpdateStreamingDistributionCommand: (output: __HttpResponse, context: __SerdeContext) => Promise<UpdateStreamingDistributionCommandOutput>;
