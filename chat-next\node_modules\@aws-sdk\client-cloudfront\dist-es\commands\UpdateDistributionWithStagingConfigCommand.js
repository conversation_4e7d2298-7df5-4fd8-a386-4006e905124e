import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { UpdateDistributionWithStagingConfigResultFilterSensitiveLog, } from "../models/models_1";
import { de_UpdateDistributionWithStagingConfigCommand, se_UpdateDistributionWithStagingConfigCommand, } from "../protocols/Aws_restXml";
export { $Command };
export class UpdateDistributionWithStagingConfigCommand extends $Command {
    static getEndpointParameterInstructions() {
        return {
            UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
            Endpoint: { type: "builtInParams", name: "endpoint" },
            Region: { type: "builtInParams", name: "region" },
            UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" },
        };
    }
    constructor(input) {
        super();
        this.input = input;
    }
    resolveMiddleware(clientStack, configuration, options) {
        this.middlewareStack.use(getSerdePlugin(configuration, this.serialize, this.deserialize));
        this.middlewareStack.use(getEndpointPlugin(configuration, UpdateDistributionWithStagingConfigCommand.getEndpointParameterInstructions()));
        const stack = clientStack.concat(this.middlewareStack);
        const { logger } = configuration;
        const clientName = "CloudFrontClient";
        const commandName = "UpdateDistributionWithStagingConfigCommand";
        const handlerExecutionContext = {
            logger,
            clientName,
            commandName,
            inputFilterSensitiveLog: (_) => _,
            outputFilterSensitiveLog: UpdateDistributionWithStagingConfigResultFilterSensitiveLog,
        };
        const { requestHandler } = configuration;
        return stack.resolve((request) => requestHandler.handle(request.request, options || {}), handlerExecutionContext);
    }
    serialize(input, context) {
        return se_UpdateDistributionWithStagingConfigCommand(input, context);
    }
    deserialize(output, context) {
        return de_UpdateDistributionWithStagingConfigCommand(output, context);
    }
}
