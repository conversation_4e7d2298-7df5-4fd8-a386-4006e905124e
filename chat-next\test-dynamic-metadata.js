/**
 * Test script to verify dynamic metadata integration
 * Run this with: node test-dynamic-metadata.js
 */

const testCustomerName = 'huj'; // Use the customer from your sample data
const baseUrl = process.env.NEXT_PUBLIC_API_URL || 'https://api-develop.driply.me';
const apiUrl = `${baseUrl}/api/chat/settings?customerName=${testCustomerName}`;

console.log('🧪 Testing Dynamic Metadata Integration');
console.log('=======================================');
console.log(`Testing customer: ${testCustomerName}`);
console.log(`API URL: ${apiUrl}`);
console.log('');

async function testDynamicMetadata() {
  try {
    console.log('📡 Fetching customer data...');
    
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    console.log(`📊 Response Status: ${response.status} ${response.statusText}`);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    console.log('✅ API Response received');
    console.log('📋 Raw API Data:');
    console.log(JSON.stringify(data, null, 2));
    console.log('');

    // Test metadata extraction (same logic as in generateMetadata)
    console.log('🔍 Extracting Metadata for Next.js:');
    console.log('====================================');
    
    if (data && data.customerName) {
      const businessName = data.businessName || 'Driply';
      const metaData = data.metaData || {};
      
      const title = metaData.title || `${businessName} Chat - ${testCustomerName}`;
      const description = metaData.description || `Chat with ${businessName} - Powered by Driply`;
      
      // Handle keywords
      let keywords;
      if (metaData.keywords) {
        keywords = typeof metaData.keywords === 'string' 
          ? metaData.keywords.split(',').map(k => k.trim())
          : Array.isArray(metaData.keywords) 
            ? metaData.keywords 
            : [metaData.keywords];
      } else {
        keywords = ['chat', testCustomerName, businessName.toLowerCase(), 'driply', 'ai', 'conversation'];
      }
      
      const image = metaData.image && metaData.image.trim() ? metaData.image : '/og-image.jpg';
      
      console.log('📝 Generated Metadata:');
      console.log(`   Title: ${title}`);
      console.log(`   Description: ${description}`);
      console.log(`   Keywords: ${Array.isArray(keywords) ? keywords.join(', ') : keywords}`);
      console.log(`   Business Name: ${businessName}`);
      console.log(`   Customer Name: ${data.customerName}`);
      console.log(`   Image: ${image}`);
      console.log('');
      
      console.log('🎯 Expected HTML Head Tags:');
      console.log(`<title>${title}</title>`);
      console.log(`<meta name="description" content="${description}">`);
      console.log(`<meta name="keywords" content="${Array.isArray(keywords) ? keywords.join(',') : keywords}">`);
      console.log(`<meta property="og:title" content="${title}">`);
      console.log(`<meta property="og:description" content="${description}">`);
      console.log(`<meta property="og:site_name" content="${businessName}">`);
      if (image !== '/og-image.jpg') {
        console.log(`<meta property="og:image" content="${image}">`);
      }
      
      console.log('');
      console.log('✅ Dynamic metadata extraction successful!');
      console.log('This data will be used for SEO metadata in the HTML head.');
      
    } else {
      console.log('⚠️  No customer data found in response');
      console.log('Will use fallback metadata');
    }

  } catch (error) {
    console.error('❌ Error testing API:', error.message);
    console.log('');
    console.log('🔄 Fallback metadata will be used:');
    console.log(`📝 Title: Chat with ${testCustomerName} - Driply`);
    console.log(`📄 Description: Start a conversation with ${testCustomerName} on Driply platform`);
    console.log(`🏷️  Keywords: chat, ${testCustomerName}, driply, ai, conversation`);
  }
}

// Run the test
testDynamicMetadata().then(() => {
  console.log('');
  console.log('🎯 Test completed!');
  console.log('');
  console.log('💡 Next steps:');
  console.log('1. Visit your app at /huj to test dynamic metadata rendering');
  console.log('2. Check browser tab title and page source');
  console.log('3. Verify metadata appears in HTML <head> section');
  console.log('4. Test with other customer names from your API');
}).catch(console.error);
