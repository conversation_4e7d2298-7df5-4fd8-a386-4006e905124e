# Driply - AI Chat Interface

A modern, responsive chat interface built with Next.js and Tailwind CSS.

## Features

- 🎨 Clean, minimalist design
- 📱 Fully responsive layout
- ⚡ Fast and optimized with Next.js 15
- 🎯 Interactive suggestion cards
- 🔍 Real-time input handling
- 🎭 Smooth animations and transitions

## Project Structure

```
src/
├── app/
│   ├── globals.css          # Global styles
│   ├── layout.js           # Root layout component
│   └── page.js             # Main page component
├── components/
│   ├── ChatInterface.jsx   # Main chat interface
│   ├── Navbar.jsx          # Navigation component
│   └── index.js            # Component exports
└── utils/
    └── constants.js        # Application constants
```

## Getting Started

1. Install dependencies:
```bash
npm install
```

2. Run the development server:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Components

### ChatInterface
The main chat interface component featuring:
- Input field with send button
- Suggestion cards for quick actions
- Responsive grid layout

### Navbar
Clean navigation bar with:
- Brand logo
- Navigation links
- Login button
- Mobile-responsive design

## Technologies Used

- **Next.js 15** - React framework
- **Tailwind CSS** - Utility-first CSS framework
- **React Hooks** - State management
- **Responsive Design** - Mobile-first approach

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

You can check out [the Next.js GitHub repository](https://github.com/vercel/next.js) - your feedback and contributions are welcome!

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.

test deployment
