import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON><PERSON>_Madefor_Text } from "next/font/google";
import "./globals.css";
import ClientWrapper from "../components/ClientWrapper";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const wixMadeforText = Wix_Madefor_Text({
  variable: "--font-wix-madefor-text",
  subsets: ["latin"],
});

export const metadata = {
  title: {
    default: "Driply - AI Chat Platform",
    template: "%s | Driply",
  },
  description:
    "Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.",
  keywords: ["AI", "chat", "conversation", "driply", "artificial intelligence"],
  authors: [{ name: "Driply Team" }],
  creator: "<PERSON><PERSON><PERSON>",
  publisher: "<PERSON><PERSON><PERSON>",
  robots: {
    index: true,
    follow: true,
  },
  openGraph: {
    type: "website",
    locale: "en_US",
    url: "https://yourdomain.com",
    siteName: "Driply",
    title: "Driply - AI Chat Platform",
    description:
      "Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.",
    images: [
      {
        url: "/og-image.jpg", // Make sure to add this image to your public folder
        width: 1200,
        height: 630,
        alt: "Driply AI Chat Platform",
      },
    ],
  },
  twitter: {
    card: "summary_large_image",
    title: "Driply - AI Chat Platform",
    description:
      "Driply is an advanced AI chat platform that provides intelligent conversations and seamless user experience.",
    images: ["/og-image.jpg"],
  },
  viewport: "width=device-width, initial-scale=1",
  themeColor: "#000000",
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${wixMadeforText.variable} antialiased`}
        suppressHydrationWarning={true}
      >
        {/* Use ClientWrapper to separate client components from server layout */}
        <ClientWrapper>{children}</ClientWrapper>
      </body>
    </html>
  );
}
