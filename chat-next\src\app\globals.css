@import "tailwindcss";
@import url('https://api.fontshare.com/v2/css?f[]=general-sans@400,500,600,700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-wix-madefor: var(--font-wix-madefor-text);
}



body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-wix-madefor-text), Arial, Helvetica, sans-serif;
}

.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Webkit */
}

/* Chat Interface Improvements */
.chat-container {
  font-family: var(--font-wix-madefor-text), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.message-text {
  line-height: 1.6;
  font-size: 16px;
  color: #374151;
}

.user-message {
  background-color: #f3f4f6;
  border-radius: 18px;
  padding: 12px 16px;
  max-width: 85%;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.bot-message {
  color: #374151;
  max-width: 85%;
  word-wrap: break-word;
  padding: 8px 0;
}

.typing-indicator {
  background-color: #f3f4f6;
  border-radius: 18px;
  padding: 12px 16px;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #9ca3af;
  margin: 0 2px;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Input styling improvements */
.chat-input {
  font-family: inherit;
  font-size: 16px;
  line-height: 1.5;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.chat-input:focus {
  outline: none;
  border-color: #d1d5db;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Smooth transitions */
.message-container {
  transition: all 0.3s ease;
}

/* ChatGPT-style message spacing and positioning */
.messages-wrapper {
  padding: 40px 0;
  min-height: 100%;
  display: flex;
  flex-direction: column;
}

.message-container {
  margin-bottom: 32px;
  transition: all 0.3s ease;
}

.message-container:last-child {
  margin-bottom: 60px; /* Extra space for the last message */
}

/* Ensure messages have proper spacing for focused viewing */
.message-container + .message-container {
  margin-top: 32px;
}

/* ChatGPT-style message positioning */
.messages-wrapper .message-container:last-child,
.messages-wrapper .latest-message {
  /* Latest message gets prominent positioning */
  scroll-margin-top: 20vh;
}

/* Enhanced focus for the latest message */
.latest-message {
  position: relative;
}

/* Smooth transitions for message appearance */
.message-container {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* Animation for new messages */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.latest-message {
  animation: messageSlideIn 0.4s ease-out;
}
