@import "tailwindcss";
@import url('https://api.fontshare.com/v2/css?f[]=general-sans@400,500,600,700&display=swap');

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --font-wix-madefor: var(--font-wix-madefor-text);
}



body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-wix-madefor-text), Arial, Helvetica, sans-serif;
}

.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE 10+ */
}
.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome/Safari/Webkit */
}

/* Chat Interface Improvements */
.chat-container {
  font-family: var(--font-wix-madefor-text), -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.message-text {
  line-height: 1.6;
  font-size: 16px;
  color: #374151;
}

.user-message {
  background-color: #f3f4f6;
  border-radius: 18px;
  padding: 12px 16px;
  max-width: 85%;
  word-wrap: break-word;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.bot-message {
  color: #374151;
  max-width: 85%;
  word-wrap: break-word;
  padding: 8px 0;
}

.typing-indicator {
  background-color: #f3f4f6;
  border-radius: 18px;
  padding: 12px 16px;
  display: inline-flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.typing-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #9ca3af;
  margin: 0 2px;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* Input styling improvements */
.chat-input {
  font-family: inherit;
  font-size: 16px;
  line-height: 1.5;
  border: 1px solid #e5e7eb;
  border-radius: 20px;
  background-color: #ffffff;
  transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.chat-input:focus {
  outline: none;
  border-color: #d1d5db;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Smooth transitions */
.message-container {
  transition: all 0.3s ease;
}

/* ChatGPT-style message layout and flow */
.messages-wrapper {
  padding: 60px 0 100px 0; /* More generous padding for better spacing */
  min-height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Core message container styling */
.message-container {
  margin-bottom: 40px; /* Increased spacing between messages */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); /* Smoother easing */
  position: relative;
}

/* Visual hierarchy: Latest messages are more prominent */
.message-container:last-child,
.latest-message {
  /* Latest message styling for prominence */
  position: relative;
  z-index: 2;
}

/* Visual hierarchy for different message types */
.recent-message {
  opacity: 0.9;
  transition: opacity 0.4s ease;
}

.previous-message {
  opacity: 0.75;
  transition: opacity 0.4s ease;
}

/* Hover effect to bring back focus to older messages */
.recent-message:hover,
.previous-message:hover {
  opacity: 1;
}

/* ChatGPT-style message entrance animation */
@keyframes chatGPTMessageEnter {
  from {
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.latest-message {
  animation: chatGPTMessageEnter 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Smooth scroll behavior for containers */
.hide-scrollbar {
  scroll-behavior: smooth;
}

/* Enhanced message spacing for different screen sizes */
@media (max-width: 768px) {
  .messages-wrapper {
    padding: 40px 0 80px 0;
  }

  .message-container {
    margin-bottom: 32px;
  }
}

@media (min-width: 1024px) {
  .messages-wrapper {
    padding: 80px 0 120px 0;
  }

  .message-container {
    margin-bottom: 48px;
  }
}
