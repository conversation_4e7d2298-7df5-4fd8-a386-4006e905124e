import React from 'react'
import ChatInterfaceWrapper from '../../components/ChatInterfaceWrapper'

// Dynamic metadata generation using your API response data
export async function generateMetadata({ params }) {
  const { slug } = await params

  // Default fallback metadata
  let title = `Chat with ${slug} - Driply`
  let description = `Start a conversation with ${slug} on Driply platform`
  let keywords = ['chat', slug, 'driply', 'ai', 'conversation']
  let businessName = 'Driply'
  let image = '/og-image.jpg'

  try {
    // Fetch the same data that your ChatContext uses
    const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'https://api-develop.driply.me'}/api/chat/settings?customerName=${slug}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Cache for better performance
      next: { revalidate: 300 }
    })

    if (response.ok) {
      const data = await response.json()
      console.log('[Metadata] API Response:', data)

      if (data && data.customerName) {
        // Extract data from your API response structure
        businessName = data.businessName || 'Driply'
        const metaData = data.metaData || {}

        // Use the metadata from your API response
        title = metaData.title || `${businessName} Chat - ${slug}`
        description = metaData.description || `Chat with ${businessName} - Powered by Driply`

        // Handle keywords - convert string to array if needed
        if (metaData.keywords) {
          keywords = typeof metaData.keywords === 'string'
            ? metaData.keywords.split(',').map(k => k.trim())
            : Array.isArray(metaData.keywords)
              ? metaData.keywords
              : [metaData.keywords]
        } else {
          keywords = ['chat', slug, businessName.toLowerCase(), 'driply', 'ai', 'conversation']
        }

        // Use custom image if provided
        if (metaData.image && metaData.image.trim()) {
          image = metaData.image
        }

        console.log('[Metadata] Using dynamic metadata:', { title, description, keywords, businessName, image })
      }
    } else {
      console.warn('[Metadata] API request failed, using fallback metadata')
    }
  } catch (error) {
    console.warn('[Metadata] Error fetching dynamic metadata:', error.message)
    // Will use fallback metadata
  }

  // Return the complete metadata object
  return {
    title: title,
    description: description,
    keywords: keywords,
    openGraph: {
      title: title,
      description: description,
      type: 'website',
      url: `https://yourdomain.com/${slug}`, // TODO: Replace with your actual domain
      siteName: businessName,
      images: [
        {
          url: image,
          width: 1200,
          height: 630,
          alt: `${businessName} Chat - ${slug}`,
        },
      ],
      locale: 'en_US',
    },
    twitter: {
      card: 'summary_large_image',
      title: title,
      description: description,
      images: [image],
    },
    robots: {
      index: true,
      follow: true,
    },
    authors: [{ name: businessName }],
    creator: businessName,
    publisher: 'Driply',
  }
}

// Server component - no "use client" directive
const page = async ({ params, searchParams }) => {
  const { slug } = await params
  const queryParams = await searchParams

  return (
    <div>
      {/* Use wrapper to avoid client component in server component */}
      <ChatInterfaceWrapper slug={slug} query={queryParams} />
    </div>
  )
}

export default page