import type { FunctionsConfigManifest, MiddlewareManifest, NextConfig, PrerenderManifest } from "../../types/next-types";
import type { PublicFiles } from "../../build";
export declare function loadConfig(nextDir: string): NextConfig;
export declare function loadBuildId(nextDir: string): string;
export declare function loadPagesManifest(nextDir: string): Record<string, string>;
export declare function loadHtmlPages(nextDir: string): string[];
export declare function loadPublicAssets(openNextDir: string): PublicFiles;
export declare function loadRoutesManifest(nextDir: string): {
    basePath: string | undefined;
    rewrites: {
        beforeFiles: import("../../types/next-types").RewriteDefinition[];
        afterFiles: import("../../types/next-types").RewriteDefinition[];
        fallback: import("../../types/next-types").RewriteDefinition[];
    };
    redirects: import("../../types/next-types").RedirectDefinition[];
    routes: {
        static: import("../../types/next-types").RouteDefinition[];
        dynamic: import("../../types/next-types").RouteDefinition[];
        data: {
            static: import("../../types/next-types").DataRouteDefinition[];
            dynamic: import("../../types/next-types").DataRouteDefinition[];
        };
    };
    locales: string[];
};
export declare function loadConfigHeaders(nextDir: string): import("../../types/next-types").Header[] | undefined;
export declare function loadPrerenderManifest(nextDir: string): PrerenderManifest;
export declare function loadAppPathsManifest(nextDir: string): Record<string, string>;
export declare function loadAppPathRoutesManifest(nextDir: string): Record<string, string>;
export declare function loadAppPathsManifestKeys(nextDir: string): string[];
export declare function loadMiddlewareManifest(nextDir: string): MiddlewareManifest;
export declare function loadFunctionsConfigManifest(nextDir: string): FunctionsConfigManifest;
